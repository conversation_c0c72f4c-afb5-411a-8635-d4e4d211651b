from typing import List, Optional, Any, Dict, Union
from pydantic import BaseModel, Field
from enum import Enum
import datetime


# Matching Response schema
class MatchInfo(BaseModel):
    match_score: str = Field(
        ...,
        description="Score indicating the quality of the match to the index. Examples include 'Exact', 'Similar; Typo', 'Alternative', or 'Unknown'.",
    )
    reason: str = Field(
        ...,
        description="Explanation for the match score, providing context about the matching decision.",
    )


class MatchScore(str, Enum):
    SIMILAR = "Similar: Typo"
    ALTERNATIVE = "Alternative"
    UNKNOWN = "Unknown"


class ParameterValueType(str, Enum):
    NUMERIC_VALUE = "numeric_value"
    NEGATIVE_POSITIVE = "negative_positive"
    OPERATOR_VALUE = "operator_value"
    BLANK = "blank"


class ConversionStatus(str, Enum):
    CONVERSION_PASS = (
        "conversion_pass"  # Successfully converted or already in the reference unit
    )
    NO_CONVERSION = "no_conversion"  # No conversion needed (same unit as reference)
    CONVERSION_FAIL = "conversion_failed"  # Conversion not possible


# Unit conversion
class StandardizedResult(BaseModel):
    result: Union[str, float, int, None] = Field(
        ...,
        description="The converted numerical result after standardization, whether conversion was done or not, including non-numeric which was not converted.",
    )
    unit: Optional[str] = Field(
        ...,
        description="The standardized unit after conversion was done or not, including units for non-numeric which was not converted .",
    )


class ConversionDetails(BaseModel):
    factor: Optional[float] = Field(
        ..., description="The numerical factor used for unit conversion."
    )
    calculation: Optional[str] = Field(
        ..., description="The mathematical expression used for conversion."
    )


class UnitConversion(BaseModel):
    status: ConversionStatus = Field(
        ...,
        description="""
            Indicates the conversion status: conversion_pass, no_conversion, or conversion_failed.

                - **`conversion_pass`** → Successfully converted to reference unit, including units which are equivalent.
                - **`no_conversion`** → Exactly same unit as reference or test result has non-numeric value.
                - **`conversion_failed`** → Conversion not possible due to incompatible units.
             """,
    )
    standardized: StandardizedResult = Field(
        ...,
        description="The converted result in the standardized unit, if conversion was successful. Otherwise display the test result and the unit from the test_params if it is already in the reference unit or conversion was not needed or conversion failed or non-numeric.",
    )
    conversion_details: Optional[ConversionDetails] = Field(
        ...,
        description="Details about the conversion process, including the conversion factor and calculation.",
    )
    comment: str = Field(
        ...,
        description="Add a comment regarding the conversion to briefly tell why conversion was done or not.",
    )


class MatchParameterInfo(BaseModel):
    match_score: MatchScore = Field(
        ...,
        description="""
        1. Similar; Typo:
          * The options
            > Parameter name:
              # Exact match in the parameter name
              # Minor variations in:
                - Case
                - Spacing
                - Punctuation
                - Units
            > The parameter appears under the correct test type OR a test type with minor variations (capitalization, spacing, etc.)
          * Examples:
            > From file: {Parameter} =  {Hemoglobin} under {Test} = {Complete Blood Count} | From Database (=index): {Parameter} = {Hemoglobin} under {test_type} = {Complete Blood Count} Result: Similar; Typo (exact match for both)
            > From file: {Parameter} =  {glucose} under {Test}= {metabolic panel} | From Database (=index): {Parameter} = {Glucose} under {test_type}= {Metabolic Panel} Result: Similar; Typo (case difference only)
            > From file: {Parameter} =  {T.S.H} under {Test} = {Thyroid Panel} | From Database (=index): {Parameter} = {TSH} under {test_type} = {Thyroid Panel} Result: Similar; Typo (punctuation difference only)
            > From file: {Parameter} =  {white blood cell} under {test} = {complete blood count} | From Database (=index): {Parameter} = {White Blood Cell} under {test_type} = {Complete Blood Count} Result: Similar; Typo (case differences only)
            > From file: {Parameter} =  {Eosinnophils} under {test} = {CBC} | From Database (=index): {Parameter} = {Eosinophils} under {test_type} = {CBC} Result: Similar; Typo (spelling error with doubled 'n')
            > From file: {Parameter} =  {Platlets} under {test} = {COMPLETE BLOOD COUNT} | From Database (=index): {Parameter} = {Platelets} under {test_type} = {Complete Blood Count} Result: Similar; Typo (spelling error and case difference)

        2. Alternative:
          * The options:
            > The parameter name uses a different but medically equivalent term (e.g., "AST" vs "SGOT")
            > OR: The parameter appears under a different but related test type (e.g., parameter "Total Cholesterol" under "Cholesterol Panel" instead of "Lipid Profile")
            > OR: Common abbreviations or full names= if the field is the most related to the index - only with alternative naming
            > Any case where either the parameter name OR its test type requires alternative medical terminology to match
          * Examples:
            > From file: {Parameter} =  {AST} under {test} = {Liver Panel} | From Database (=index): {Parameter} = {SGOT} under {test_type} = {Hepatic Function} Result: Alternative (different name for same enzyme, related test types)
            > From file: {Parameter} =  {HbA1c} under {test} = {Diabetic Profile} | From Database (=index): {Parameter} = {Hemoglobin A1c} under {test_type} = {Diabetes Panel} Result: Alternative (abbreviated parameter name and alternative test type naming)
            > From file: {Parameter} =  {Total Cholesterol} under {test} = {Cardiac Risk Assessment} | From Database (=index): {Parameter} = {Total Cholesterol} under {test_type} = {Lipid Profile} Result: Alternative (same parameter but under related alternative test type)
            > From file: {Parameter} =  {Neutrophils %} under {test} = {White Cell Count} | From Database (=index): {Parameter} = {Neutrophil Percentage} under {test_type} = {Complete Blood Count} Result: Alternative (similar parameter with slight naming difference, related test types)
            > From file: {Parameter} =  {Creatinine} under {test} = {Kidney Function} | From Database (=index): {Parameter} = {Serum Creatinine} under {test_type} = {Metabolic Panel} Result: Alternative (essentially same parameter with minor name difference, different but related test type)
            > From file: {Parameter} =  {LFTs} under {test} = {Hepatic Assessment} | From Database (=index): {Parameter} = {Liver Function Tests} under {test_type} = {Metabolic Panel} Result: Alternative (acronym vs full name, alternative test type naming)
            > From file: {Parameter} =  {Vitamin D 25-OH} under {test} = {Nutrition Panel} | From Database (=index): {Parameter} = {25-Hydroxyvitamin D} under {test_type} = {Vitamin Analysis} Result: Alternative (alternative naming for same compound, related test types)

        3. Unknown
          * The options:
            > The parameter has no equivalent in the index;
            > Any case where the parameter-test type combination cannot be reasonably matched to the index
          * Example:
            > From file: {Parameter} =  {Novel Biomarker XYZ} under {test} = {Cancer Screening} | From index: No equivalent parameter found Result: Unknown (parameter doesn't exist in index)
            > From file: {Parameter} =  {CA-125} under {test} = {Tumor Markers} | From index: No equivalent {parameter} or {test_type}  Result: Unknown (specialized test not in index)

        Additional information:
            - Please return the Unknown Parameters in the same format, without being Nested under Test; Sample.
            - Please analyse and return ALL the results in the report.
            - Please list everything in English.
            - Please do not make up information.
        """,
    )
    reason: str = Field(
        ...,
        description="Explanation for the match score, providing context about the matching decision.",
    )


class MatchTestTypeInfo(BaseModel):
    match_score: MatchScore = Field(
        ...,
        description="""
        1. Similar; Typo = The options:
            - if the field is identical to the index (written exactly in the same way and formatting, same letters same word, or same sequel of words)
            - if the field resembled the index (written exactly the same way, using the same letters, word or sequel of words) - only may include a typo, or have differences in small/capital letters
            Examples:
                - From file: {Test} = {Complete Blood Count (CBC)} |From Database (=index): {Test_Type} = {Complete Blood Count (CBC)}
                - From file: {Test} = {Metabolic Panel} | From Database (=index): {Test_Type} = {metabolic panel}
        2. Alternative = if the field is the most related to the index - only with alternative naming
            Example:
                - From file: {Test} = {Cholesterol Panel} | From Database (=index): {Test_Type} = {Lipid Profile}
                - From file: {Test} = {Lipid Panel} | From Database (=index): {Test_Type} = {Lipid Profile}
        3. Unknown = the field was not found in the index
            Example
                - From file: {Test_Type} = {ENA test} | From Database (=index): {Test_Type} = {--}
        """,
    )
    reason: str = Field(
        ...,
        description="Explanation for the match score, providing context about the matching decision.",
    )


class ParameterValueTypeInfo(BaseModel):
    value_type: ParameterValueType = Field(
        ...,
        description="""
        1. Numeric value = Accepts numbers (including negatives and fractions like 0.5. e.g. "27", "32.1", "100.01", "0.02", "5", etc.).
        2. Negative / Positive = Only allows selection of positive or negative (for example: "POSITIVE", "NEGATIVE", "Positive", "Negative", "negative", "positive", "שלילי", "חיובי", etc. ).
        3. Operator value = Accepts numbers (including negatives and fractions) and operators (e.g., <, >, +, >=, <=).
        4. Blank = No value type determined.
        """,
    )


class TestTypeMatchScore(str, Enum):
    MATCH = "Match"
    SIMILAR = "Similar: Typo"
    ALTERNATIVE = "Alternative"
    UNKNOWN = "Unknown"


class TestTypeMatchInfo(BaseModel):
    match_score: TestTypeMatchScore = Field(
        ...,
        description="""
        Match classification for test types:
        - Match: Exact match with index
        - Similar: Typo: Match with minor differences (typos, case)
        - Alternative: Match using alternative naming
        - Unknown: Test type not found in index
        """,
    )
    reason: str = Field(
        ...,
        description="Explanation for the test type match score, providing context about the matching decision.",
    )


class PatientInfo(BaseModel):
    first_name: str = Field(
        ...,
        description="The first name of the patient, extracted directly from the uploaded lab test report.",
    )
    last_name: str = Field(
        ...,
        description="The last name of the patient, extracted directly from the uploaded lab test report.",
    )
    patient_id: int = Field(
        ...,
        description="The unique identifier for the patient, extracted directly from the uploaded lab test report.",
    )
    match_info: MatchInfo = Field(
        ...,
        description="Matching information for the patient details, compared against the Users index.",
    )


class PhysicianInfo(BaseModel):
    first_name: str = Field(
        ...,
        description="The first name of the referring or treating physician, extracted from the uploaded lab test report.",
    )
    last_name: str = Field(
        ...,
        description="The last name of the referring or treating physician, extracted from the uploaded lab test report.",
    )
    doctor_id: int = Field(
        ...,
        description="The unique identifier for the physician, extracted from the Permissions index.",
    )
    match_info: MatchInfo = Field(
        ...,
        description="Matching information for the physician details, compared against the Permissions index.",
    )


class MedicalFacility(BaseModel):
    facility_name: str = Field(
        ...,
        description="The name of the medical facility where the tests were conducted, extracted from the lab test report.",
    )
    matched_facility_name: str = Field(
        ...,
        description="The name of the medical facility matched against the Institutes index.",
    )
    matched_facility_id: int = Field(
        ...,
        description="The unique identifier of the matched medical facility, derived from the Institutes index.",
    )
    location: Optional[str] = Field(
        None,
        description="The geographical location of the medical facility, if available, extracted and matched with the Institutes index.",
    )
    match_info: MatchInfo = Field(
        ...,
        description="Matching information for the facility details, compared against the Institutes index.",
    )


class LabTestParameter(BaseModel):
    name: str = Field(
        ...,
        description="""
                The parameter name exactly as displayed in the lab report. Extract the full name including any prefixes, suffixes, or codes.
                Examples in English: 'Glucose', 'WBC', 'HDL Cholesterol', 'AST (SGOT)', 'TSH (Thyroid Stimulating Hormone)', 'BUN (Blood Urea Nitrogen)', 'MCHC', 'Vitamin D, 25-OH', 'Anti-Thyroid Peroxidase Ab', 'T. gondii IgG'.
                Examples in Hebrew: 'גלוקוז', 'ספירת תאים לבנים', 'כולסטרול HDL', 'אלנין אמינוטרנספראז (ALT)', 'הורמון מעורר בלוטת התריס (TSH)', 'אוריאה בדם (BUN)', 'אלבומין', 'תאי דם אדומים (RBC)', 'המוגלובין'.
                For bilingual entries, preserve exactly as shown: 'ALT/אלנין אמינוטרנספראז', 'כולסטרול HDL/HDL Cholesterol'.
                For special characters or formatting, maintain as presented: 'β-hCG', 'α-Fetoprotein', 'גאמא GT', 'Vitamin B₁₂'.
                Field naming variations in English: 'Test', 'Parameter', 'Analyte', 'Component', 'Measurement', 'Test Name', 'Exam', 'Determination', 'Assay'.
                Field naming variations in Hebrew: 'פרמטר', 'בדיקה', 'מדד', 'רכיב', 'אנליט', 'שם הבדיקה', 'מרכיב', 'קטלוג', 'פריט'.
            """,
    )

    result: Union[str, float, int, None] = Field(
        ...,
        description="""
        The patient's actual test measurement or finding.
        - Extract only the patient's specific result value.
        - Do not include any reference values or normal ranges here.
        - Look for the value that appears before units or as the primary finding.
        - Example: In '70 mg/dl', extract '70' as the result.
        - For Hebrew text results, translate to English:
            * 'חיובי' → 'Positive'
            * 'תקין' → 'Normal'
            * 'לא נמצא' → 'Not Found'
            * 'לא בוצע, דגימה מולחמית' → 'Not performed, hemolyzed sample'
        - Keep numeric values unchanged (e.g., '5.2' stays as '5.2')
    """,
    )

    range: Optional[str] = Field(
        None,
        description="""
        The expected normal or reference values for comparison.
        - Look for values labeled as "range", "normal", "reference", or appearing in comments.
        - Include single values that appear in comments with matching units.
        - Extract complete ranges like '3.5-5.2' or '10-100'.
        - For a single reference value like '283 mg/dl' in comments, extract '283'.
        - For Hebrew text, translate to English with standardized operators:
            * 'קטן מ' → '<' (smaller than becomes less than)
            * 'גדול מ' → '>' (greater than remains greater than)
            * 'עד' → '<=' (up to)
            * 'גברים:' → 'Men:'
            * 'נשים:' → 'Women:'
        Examples:
            * 'קטן מ-200' → '<200'
            * 'גדול מ-50' → '>50'
            * 'עד 100' → '<=100'
            * 'גברים: 13.5-17.5, נשים: 12.0-15.5' → 'Men: 13.5-17.5, Women: 12.0-15.5'
        - If no reference range is provided, set to null.
    """,
    )

    units: Optional[str] = Field(
        None,
        description="""
                The unit of measurement for the test result and reference ranges. Extract exactly as shown, including special characters and formatting.
                Examples include:
                    - Basic units: 'mg/dL', 'g/dL', 'μg/dL', 'ng/mL', 'pg/mL', 'mEq/L', 'mmol/L', 'μmol/L', 'U/L', 'IU/L', 'mIU/L', '%', 'g/L'.
                    - Cell count units: 'x10^9/L', 'x10^12/L', 'x10³/μL', 'x10⁶/μL', '/μL', '/hpf', '/lpf', '/mm³'.
                    - Time-based units: 'sec', 'min', 'hr', 'mL/min', 'mL/min/1.73m²'.
                    - Ratio units: 'ratio', 'index', 'AU', 'S/CO', 'titer'.
                    - Hebrew units: 'מ״ג/ד״ל', 'גרם/ד״ל', 'יחידות/ליטר', 'מ״מול/ליטר', 'אחוזים', 'שניות'.
                    - Combined units: 'cells/mm³', 'mmHg', 'gr/dL', 'fL', 'pg/cell'.
                For qualitative results where no unit is applicable (like 'Positive'/'Negative'), set to null.
                If units appear with ranges ('3.5-5.0 mg/dL'), extract only the unit portion ('mg/dL').
                Field naming variations in English: 'Units', 'Unit', 'Measurement Units', 'Unit of Measure', 'UOM', 'Reported In', 'Scale', 'Metrics', 'Units of Measurement'.
                Field naming variations in Hebrew: 'יחידות', 'יחידת מידה', 'מידה', 'סקלה', 'יחידות מדידה', 'נמדד ב', 'יח׳', 'יח׳ מדידה', 'מטריקה'.
            """,
    )

    test_type: Optional[str] = Field(
        None,
        description="""
                The category or group of laboratory tests that the parameter belongs to. Usually appears as a header or section title.
                Examples include:
                    -Examples in English: 'Complete Blood Count (CBC)', 'Comprehensive Metabolic Panel (CMP)', 'Lipid Profile', 'Thyroid Function Tests', 'Liver Function Tests', 'Urinalysis', 'Hormone Panel', 'Allergy Panel', 'Infectious Disease Serology', 'Vitamin Panel', 'Electrolytes', 'Iron Studies', 'Coagulation Studies'.
                    -Examples in Hebrew: 'ספירת דם מלאה', 'פאנל מטבולי מקיף', 'פרופיל שומנים', 'בדיקות תפקודי בלוטת התריס', 'תפקודי כבד', 'בדיקת שתן', 'פאנל הורמונלי', 'בדיקות אלרגיה', 'סרולוגיה למחלות זיהומיות', 'בדיקות ויטמינים', 'אלקטרוליטים', 'בדיקות ברזל', 'בדיקות קרישה'.
                    -Bilingual examples: 'ספירת דם מלאה (CBC)', 'בדיקות תפקודי כבד (Liver Function Tests)', 'פרופיל שומנים (Lipid Profile)'.
                If test type is not explicitly stated, attempt to infer from context, parameter grouping, or report structure.
                For standalone tests, use a general category: 'Specialized Tests', 'בדיקות מיוחדות'. "
                Field naming variations in English: 'Test Type', 'Panel', 'Profile', 'Test Category', 'Battery', 'Test Group', 'Panel Type', 'Test Classification', 'Laboratory Section', 'Department'. "
                Field naming variations in Hebrew: 'סוג בדיקה', 'פאנל', 'פרופיל', 'קטגוריית בדיקה', 'סוללת בדיקות', 'קבוצת בדיקות', 'סיווג בדיקה', 'מחלקה מעבדתית', 'מדור'.
                """,
    )

    comment: Optional[str] = Field(
        None,
        description="""
                Any additional notes, interpretations, or recommendations related to the test result.
                Extract the complete text as shown.
                Examples include:
                    - Clinical interpretation examples: 'Consistent with iron deficiency anemia', 'Suggestive of viral infection', 'Compatible with hypothyroidism', 'May indicate dehydration', 'Consider renal function assessment'.
                    - Sample quality issues: 'Hemolyzed sample', 'Lipemic specimen', 'Sample insufficient for complete analysis', 'Results may be affected by medication', 'Recommend recollection'. "
                    - Follow-up recommendations: 'Recommend follow-up in 3 months', 'Consider endocrinology referral', 'Repeat test after fasting', 'Correlate with clinical symptoms'.
                    - Technical notes: 'Verified by repeat analysis', 'Confirmed by alternate method', 'Manual differential performed', 'Second sample requested'.
                    - Hebrew examples: 'מתאים לאנמיה מחוסר ברזל', 'מרמז על זיהום ויראלי', 'ממצאים תואמים תת-פעילות בלוטת התריס', 'מומלץ מעקב תוך 3 חודשים', 'דגימה המוליטית', 'יש לשקול הפניה לאנדוקרינולוג', 'מומלץ לחזור על הבדיקה בצום'.
                    - Alerts: 'Critical value called to Dr. Smith on 03/11/2025', 'Results require immediate clinical attention', 'ערך קריטי, דווח לד״ר כהן ב-11/03/2025', 'תוצאות מחייבות התייחסות קלינית מיידית'.
                Comments may appear in footnotes, special sections, or directly adjacent to results. Include all relevant comments for the specific parameter.
                Field naming variations in English: 'Comment', 'Notes', 'Remarks', 'Interpretation', 'Clinical Notes', 'Additional Information', 'Observation', 'Comment/Interpretation', 'Clinical Significance', 'Special Notes'.
                Field naming variations in Hebrew: 'הערה', 'הערות', 'פירוש', 'פרשנות', 'הערות קליניות', 'מידע נוסף', 'תצפית', 'משמעות קלינית', 'הערות מיוחדות', 'הסבר'.
            """,
    )

    comment_english: Optional[str] = Field(
        None,
        description="""
                English translation of the comment field when the original comment is in Hebrew.
                This field should only be populated when the comment field contains Hebrew text.
                The translation should maintain medical terminology accuracy and context.
                If the original comment is already in English, this field should remain empty (null).
                Examples:
                    - Hebrew comment: 'מתאים לאנמיה מחוסר ברזל' → comment_english: 'Consistent with iron deficiency anemia'
                    - Hebrew comment: 'מרמז על זיהום ויראלי' → comment_english: 'Suggestive of viral infection'
                    - Hebrew comment: 'מומלץ מעקב תוך 3 חודשים' → comment_english: 'Recommend follow-up in 3 months'
                    - Hebrew comment: 'יש לשקול הפניה לאנדוקרינולוג' → comment_english: 'Consider endocrinology referral'
                    - Hebrew comment: 'ערך קריטי, דווח לד״ר כהן ב-11/03/2025' → comment_english: 'Critical value, reported to Dr. Cohen on 11/03/2025'
            """,
    )

    index: int = Field(None, description="The index of the lab test parameter.")

    result_value_type: ParameterValueType = Field(
        ...,
        description="""
            The value type of the lab test result from test_params.result based on the categories defined in ParameterValueTypeInfo.
        Each parameter result must be classified into a single **value type** from the following categories:
            1. Numeric value = The result is a number (including negatives and fractions like 0.5). Return as **numeric_value** only.
            2. Negative / Positive = The result is a positive or negative. An example is "Negative", "Positive", "Pos", "Neg". Return as **positive_negative** only.
            3. Operator value = The result is a number (including negatives and fractions) combined with an operator, or a number with a compound expression (e.g., <, >, +, >=, <=). An example is <2, +50, less than 3, greater than 4, etc. Return as **operator_value** only.
            4. Blank = The result is textual and does not fit any of the above categories (e.g., "Normal", "Undeteramable", "See attached report", "", null). Return as **blank** only.
        """,
    )


class MatchData(BaseModel):
    id: Optional[int] = Field(
        ...,
        description="The unique identifier of the matched lab test, derived from the matched context data.",
    )
    test_type: Optional[str] = Field(
        ...,
        description="The test type of the matched lab test, derived from the matched context data.",
    )
    parameter: Optional[str] = Field(
        ...,
        description="The parameter of the matched lab test, derived from the matched context data.",
    )
    sample_type: Optional[str] = Field(
        ...,
        description="The sample type of the matched lab test, derived from the matched context data.",
    )


class LabReportItem(BaseModel):
    # test_params: LabTestParameter = Field(
    #     ..., description="Contains detailed information about the lab test, directly extracted from the lab test response. Ensure this field is populated with precise data from the response."
    # )
    match_data: MatchData = Field(
        ...,
        description="The lab matched data derived from the most correlating from the context.",
    )
    match_parameter_info: MatchParameterInfo = Field(
        ...,
        description="Matching information for the lab test details using the paramter name, compared against the context",
    )
    match_test_type_info: MatchTestTypeInfo = Field(
        ...,
        description="Matching information for the lab test details using the test type, compared against the context",
    )
    # parameter_value_type: ParameterValueTypeInfo = Field(
    #     ...,
    #     description="Specifies the value type of the lab test result from test_params.result based on the categories defined in ParameterValueTypeInfo.",
    # )
    unit_conversion: UnitConversion = Field(
        description="Details of unit conversion, if applicable, for standardizing numerical lab test results."
    )


class TestReportSchema(BaseModel):
    patient_info: PatientInfo = Field(
        ...,
        description="Details of the patient, including their name and unique identifier.",
    )
    physician_info: PhysicianInfo = Field(
        ...,
        description="Details of the referring or treating physician, including their name.",
    )
    medical_facility: MedicalFacility = Field(
        ...,
        description="Details of the medical facility where the tests were conducted.",
    )
    lab_reports: List[LabReportItem] = Field(
        ...,
        description="A collection of all the lab test results from the results response data.",
    )


# File content response schema
class UploadedPatientInfo(BaseModel):
    first_name: Optional[str] = Field(
        ...,
        description="The first name of the patient, extracted directly from the uploaded lab test report.",
    )
    last_name: Optional[str] = Field(
        ...,
        description="The last name of the patient, extracted directly from the uploaded lab test report.",
    )


class UploadedPhysicianInfo(BaseModel):
    first_name: Optional[str] = Field(
        ...,
        description="The first name of the referring or treating physician, extracted from the uploaded lab test report.",
    )
    last_name: Optional[str] = Field(
        ...,
        description="The last name of the referring or treating physician, extracted from the uploaded lab test report.",
    )


class UploadedMedicalFacility(BaseModel):
    facility_name: Optional[str] = Field(
        ...,
        description="The name of the medical facility where the tests were conducted, extracted from the lab test report.",
    )
    location: Optional[str] = Field(
        None,
        description="The geographical location of the medical facility, if available, extracted and matched with the Institutes index.",
    )


class UploadedFileContent(BaseModel):
    patient_info: UploadedPatientInfo = Field(
        None,
        description="Details of the patient details in the uploaded lab report",
    )
    physician_info: UploadedPhysicianInfo = Field(
        None,
        description="Details of the referring or treating physician.",
    )
    medical_facility: UploadedMedicalFacility = Field(
        None,
        description="Details of the medical facility where the tests were conducted.",
    )
    is_lab_report: bool = Field(
        ...,
        description="Determine if the document is a lab report by carefully analyzing key medical indicators like test results, reference ranges, patient vitals, lab facility details, physician information, and medical terminology. The document should contain structured clinical/laboratory data before being classified as a lab report.",
    )
    test_date: Optional[datetime.datetime] = Field(
        None, description="The date when the lab test was conducted or reported."
    )
    lab_reports: List[LabTestParameter] = Field(
        default_factory=list,
        description="A comprehensive list of all laboratory test results and parameters from the report. This should include test names, numeric values, units, reference ranges, and any flags or annotations. The extraction should be thorough and complete, capturing both routine and specialized tests, without omitting any results present in the source document.",
    )


class LabTestReportExtraction(BaseModel):
    lab_reports: List[LabTestParameter] = Field(
        default_factory=list,
        description="A collection of all lab test results extracted from the markdown data, containing test names, values, reference ranges, units and other relevant parameters",
    )


# Physician matching
class MatchingPhysicianInfo(BaseModel):
    matched_id: int = Field(
        ...,
        description="The id for the matched physician, extracted from the context.",
    )
    matched_title: str = Field(
        ...,
        description="The title for the matched physician, extracted from the context.",
    )
    matched_name: str = Field(
        ...,
        description="The doctorName for the matched physician, extracted from the context.",
    )
    matched_lastname: str = Field(
        ...,
        description="The doctorLastName for the matched physician, extracted from the context.",
    )
    match_info: MatchInfo = Field(
        ...,
        description="Matching information for the physician details, compared against the extracted context data",
    )


# Institution matching
class MatchMedicalFacility(BaseModel):
    value_name: str = Field(
        ...,
        description="The value data from the context matched institution, extracted from the context.",
    )
    matched_display_name: str = Field(
        ...,
        description="The display name for the matched institution, extracted from the context.",
    )
    matched_id: int = Field(
        ...,
        description="The id of the matched medical facility, extracted from the context.",
    )
    match_info: MatchInfo = Field(
        ...,
        description="Matching information for the facility details, compared against the extracted context data.",
    )


# Lat reports matching
class MatchLabReports(BaseModel):
    lab_reports: List[LabReportItem] = Field(
        ...,
        description="A collection of all the lab test results from the lab reports data. Return all the lab report data and don't exclude any.",
    )


# Routes defined schema
class Doctor(BaseModel):
    id: int
    doctorName: str
    title: str
    doctorLastName: str


class Institution(BaseModel):
    id: int
    value: str
    displayName: str


class LabTest(BaseModel):
    sample_type: str
    test_type: str
    parameter: str
    id: int


class OCRRequest(BaseModel):
    doctors: List[Doctor]
    institutions: List[Institution]
    sample_lab_tests: List[LabTest]
    language: str
    user_id: int


# ----------------- REDIS -------------------
class ProcessingStage(str, Enum):
    DOCUMENT_SUMMARY = "document_summary"
    PHYSICIAN_MATCHING = "physician_matching"
    INSTITUTION_MATCHING = "institution_matching"
    LAB_REPORT_MATCHING = "lab_report_matching"


class StageStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETE = "complete"
    FAILED = "failed"


class StageData(BaseModel):
    progress: int = Field(ge=0, le=100)
    status: StageStatus = StageStatus.PENDING


class StageUploadedFileContent(UploadedFileContent):
    test_date: Optional[str]


class StageDataSummary(StageData):
    data: Optional[dict]


class StageDataPhysician(StageData):
    data: Optional[dict]


class StageDataInstitution(StageData):
    data: Optional[dict]


class StageDataLabReport(StageData):
    data: List[Dict[str, Any]] = Field(default_factory=list)


class StageReport(BaseModel):
    stage: ProcessingStage
    message: str


class ReportStatus(BaseModel):
    user_id: str | int
    document_summary: StageDataSummary
    matched_physician: StageDataPhysician
    matched_institution: StageDataInstitution
    lab_reports: StageDataLabReport
    analysis: StageReport
