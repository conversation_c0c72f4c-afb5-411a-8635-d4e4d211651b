name: Mediboard OCR-LLM Server Deployment Pipeline
on:
  push:
    branches: [deploy]
  workflow_dispatch:

jobs:
  deploy-container-app:
    runs-on: ubuntu-latest
    env:
      IMAGE_URL: ${{ secrets.MEDIBOARD_AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.MEDIBOARD_AWS_REGION }}.amazonaws.com
      ECR_URL: ${{ secrets.MEDIBOARD_AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.MEDIBOARD_AWS_REGION }}.amazonaws.com/mediboard-ocr-llm:latest
      AWS_REGION: ${{ secrets.MEDIBOARD_AWS_REGION }}
      TARGET_DIR: /home/<USER>/ocr

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.MEDIBOARD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.MEDIBOARD_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.MEDIBOARD_AWS_REGION }}

      - name: Log in to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ env.ECR_URL }}

      - name: Create deploy script
        run: |
          cat > deploy.sh << EOF
          #!/bin/bash
          set -x
          echo "Starting deployment..."
          cd ${{ env.TARGET_DIR }}

          echo "Logging into AWS ECR..."
          aws ecr get-login-password --region ${{ env.AWS_REGION }} | docker login --username AWS --password-stdin ${{ env.IMAGE_URL }}

          echo "Stopping running containers..."
          docker-compose down || true

          echo "Removing local untagged Docker images..."
          docker image prune -f --filter "dangling=true"

          echo "Pulling the latest image from ECR..."
          docker-compose pull

          echo "Starting the Docker containers..."
          docker-compose up -d

          echo "Deployment complete."
          EOF
          chmod +x deploy.sh

      - name: Transfer Monitoring Stack
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.MEDIBOARD_OCR_PUBLIC_INSTANCE_IP }}
          username: ec2-user
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: ${{ github.workspace }}/monitoring-stack/grafana
          target: "${{ env.TARGET_DIR }}"
          strip_components: 3
          overwrite: true

      - name: Transfer Monitoring Stack
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.MEDIBOARD_OCR_PUBLIC_INSTANCE_IP }}
          username: ec2-user
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: ${{ github.workspace }}/monitoring-stack/prometheus
          target: "${{ env.TARGET_DIR }}"
          strip_components: 3
          overwrite: true

      - name: Transfer Monitoring Stack
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.MEDIBOARD_OCR_PUBLIC_INSTANCE_IP }}
          username: ec2-user
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: ${{ github.workspace }}/monitoring-stack/alertmanager
          target: "${{ env.TARGET_DIR }}"
          strip_components: 3
          overwrite: true

      - name: Transfer Configuration Files
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.MEDIBOARD_OCR_PUBLIC_INSTANCE_IP }}
          username: ec2-user
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "nginx.conf,docker-compose.yml,deploy.sh"
          target: "${{ env.TARGET_DIR }}"
          strip_components: 0
          overwrite: true

      - name: Add .env File with Secrets
        run: |
          INSTANCE_ID=$(aws ec2 describe-instances \
            --filters "Name=private-ip-address,Values=${{ secrets.MEDIBOARD_OCR_PRIVATE_INSTANCE_IP }}" \
            --query "Reservations[0].Instances[0].InstanceId" --output text)
          aws ssm send-command \
            --instance-ids $INSTANCE_ID \
            --document-name "AWS-RunShellScript" \
            --comment "Create .env file with secrets" \
            --parameters '{"commands":[
              "SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id '${{ secrets.MEDIBOARD_OCR_SECRETS }}' --query SecretString --output text)",
              "echo \"$SECRET_JSON\" | jq -r \". | to_entries | .[] | \\\"\\(.key)=\\(.value)\\\"\" > ${{ env.TARGET_DIR }}/.env"
            ]}' \
            --output text

      - name: Run Docker Compose on Server
        run: |
          INSTANCE_ID=$(aws ec2 describe-instances \
            --filters "Name=private-ip-address,Values=${{ secrets.MEDIBOARD_OCR_PRIVATE_INSTANCE_IP }}" \
            --query "Reservations[0].Instances[0].InstanceId" --output text)
          echo "Running Docker Compose on instance: $INSTANCE_ID"

          aws ssm send-command \
            --instance-ids $INSTANCE_ID \
            --document-name "AWS-RunShellScript" \
            --parameters '{
              "commands":[
                "cd /home/<USER>/ocr",
                "chmod +x deploy.sh",
                "./deploy.sh"
              ]
            }' \
            --output text

      - name: Wait for 60 seconds
        run: |
          echo "Waiting for 60 seconds..."
          sleep 65
          echo "Wait complete"

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: mediboard-notifications
          SLACK_COLOR: ${{ job.status }}
          SLACK_TITLE: MEDIBOARD-OCR-LLM Deployment Status
          SLACK_ICON_EMOJI: ":bell:"
          SLACK_MESSAGE_ON_SUCCESS: "MEDIBOARD-OCR-LLM has been successfully deployed :rocket: . Access it here: http://ec2-3-75-220-58.eu-central-1.compute.amazonaws.com/"
          SLACK_WEBHOOK: ${{ secrets.BUILD_NOTIFS_SLACK_WEBHOOK }}
