import asyncio
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.routes.ocr import ocr_router
from app.routes.redis_router import redis_router
from app.routes.check import check_router
from app.routes.test import test_router

app = FastAPI()

origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "Hello, FastAPI!"}

# app.include_router(chat_router)
app.include_router(ocr_router)
app.include_router(redis_router)
app.include_router(check_router)
app.include_router(test_router)


def run_uvicorn():
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    asyncio.run(run_uvicorn())

