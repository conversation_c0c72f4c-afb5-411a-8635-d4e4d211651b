import time
import json

# relative imports
from app.core.config import settings
from app.model.ocr_model import (
    UploadedFileContent,
    MatchingPhysicianInfo,
    MatchMedicalFacility,
    LabReportItem,
)

# other imports
from dotenv import load_dotenv
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

from enum import Enum
from typing import Any, Dict, List

# langchain imports
from langchain_openai import ChatOpenAI
from app.services.lanfuse_evaluation import LangfuseManager
from langchain_anthropic import ChatAnthropic
from app.services.lamma_index_api import LlamaIndexAPI
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from langchain_nomic import NomicEmbeddings
from langchain_community.embeddings import HuggingFaceBgeEmbeddings
from langchain.output_parsers import OutputFixingParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.runnables.base import Runnable
from langchain.callbacks.base import Base<PERSON>allback<PERSON><PERSON><PERSON>
from langchain.embeddings.base import Embeddings
import datetime
from app.services.redis_session_manager import RedisSessionManager
from app.services.webhook_service import WebhookService
from app.util.vectors import VectorDataManager, VectorDataType
from app.util.test_prompts import lab_report_matching, lab_extraction
from app.util.slack_notifier import send_slack_notification
from app.util.discord_notifier import send_discord_notification

# for slack notification
from openai import (
    APIConnectionError,
    APITimeoutError,
    AuthenticationError,
    BadRequestError,
    ConflictError,
    InternalServerError,
    NotFoundError,
    PermissionDeniedError,
    RateLimitError,
    UnprocessableEntityError,
)
from anthropic import (
    APIError,
    APIConnectionError as AnthropicConnectionError,
    APIStatusError,
    AuthenticationError as AnthropicAuthError,
    BadRequestError as AnthropicBadRequestError,
    PermissionDeniedError as AnthropicPermissionError,
    NotFoundError as AnthropicNotFoundError,
    UnprocessableEntityError as AnthropicUnprocessableError,
    RateLimitError as AnthropicRateLimitError,
    InternalServerError as AnthropicInternalError,
    APITimeoutError as AnthropicTimeoutError
)
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

# Initialize webhook service
webhook_service = WebhookService()

from app.model.ocr_model import (
    StageDataPhysician,
    StageDataInstitution,
    StageDataLabReport,
    StageReport,
    ProcessingStage,
    StageStatus,
    LabTestReportExtraction,
)
from app.services.task_manager import task_manager
from app.util.logger import logger
from app.util.text_splitter import split_text_with_count
from langchain_groq import ChatGroq
from app.services.data_processing import DataProcessor

load_dotenv()

lamma_index_service = LlamaIndexAPI()
langfuse_manager = LangfuseManager()
data_processor = DataProcessor()


class ModelType(Enum):
    OPENAI = "openai"
    CLAUDE = "claude"
    GROQ = "groq"


class ModelLoggingCallbackHandler(BaseCallbackHandler):
    def __init__(self, model_name: str):
        self.model_name = model_name

    def on_llm_start(self, serialized, prompts, **kwargs):
        logger.info(f"Model {self.model_name} is starting execution.")

    def on_llm_end(self, response, **kwargs):
        logger.info(f"Model {self.model_name} has completed execution.")


class LLMServices:
    def __init__(self):

        # HF embedding model setup
        # model_name = "BAAI/bge-small-en"
        # model_kwargs = {"device": "cpu"}
        # encode_kwargs = {"normalize_embeddings": True}

        logger.info("Initializing LLM Services")
        self.openai_api_key = settings.OPENAI_API_KEY
        self.claude_api_key = settings.CLAUDE_API_KEY
        self.nomic_api_key = settings.NOMIC_API_KEY
        self.grok_api_key = settings.GROQ_API_KEY

        logger.info("Initializing embeddings")
        self.openai_embedding = OpenAIEmbeddings(openai_api_key=settings.OPENAI_API_KEY)
        self.nomic_embedding = NomicEmbeddings(model="nomic-embed-text-v1.5")
        # self.hf_nomic_embedding = HuggingFaceBgeEmbeddings(model_name=model_name, model_kwargs=model_kwargs, encode_kwargs=encode_kwargs)

    def _get_model(
        self,
        type: ModelType,
        model_name: str,
        temperature=1,
        max_tokens=4096,
    ) -> Runnable:
        try:
            logger.info(f"Getting model of type {type} with name {model_name}")
            callback_handler = ModelLoggingCallbackHandler(model_name=model_name)
            if type == ModelType.OPENAI:
                return ChatOpenAI(
                    model_name=model_name,
                    api_key=self.openai_api_key,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    callbacks=[callback_handler],
                )
            elif type == ModelType.CLAUDE:
                return ChatAnthropic(
                    model_name=model_name,
                    anthropic_api_key=self.claude_api_key,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    callbacks=[callback_handler],
                )
            elif type == ModelType.GROQ:
                return ChatGroq(
                    model_name=model_name or "llama-3.3-70b-versatile",
                    api_key=self.grok_api_key,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    callbacks=[callback_handler],
                )
            else:
                raise ValueError(f"Unsupported model type: {type}")
        except Exception as e:
            logger.exception(e)
            raise

    def _vectorize_data(self, data: List[Dict[str, Any]]):
        """
        Converts an array of dictionaries into a Chroma retriever object.

        :param data: List of dictionaries representing the CSV data to be processed.
        :return: A Chroma retriever object ready for use.
        """
        try:
            logger.info("Vectorizing data")
            # Initialize lists to store all documents and metadata
            all_documents: List[str] = []
            all_metadatas: List[Dict[str, Any]] = []
            all_ids: List[str] = []

            for idx, data in enumerate(data):
                # Step 1: Process each dictionary into a document
                document = " ,".join([f"{key}={value}" for key, value in data.items()])

                # Create metadata as a JSON object, converting non-string/int types to string
                metadata = {
                    key: (str(value) if not isinstance(value, (str, int)) else value)
                    for key, value in data.items()
                }

                # Append to the aggregated lists
                all_documents.append(document)
                all_metadatas.append(metadata)
                all_ids.append(f"doc_{idx}")

            # embedding=OpenAIEmbeddings()

            vectorstore = FAISS.from_texts(all_documents, embedding=self.embeddings)
            retriever = vectorstore.as_retriever()
            logger.info("Data vectorization complete")
            return retriever

        except Exception as e:
            logger.exception(e)
            raise

    def send_error_notification(self, message):
        # # Send to both Slack and Discord
        # try:
        #     send_slack_notification(message)
        # except Exception as e:
        #     logger.error(f"Failed to send Slack notification: {e}")
            
        try:
            send_discord_notification(message)
        except Exception as e:
            logger.error(f"Failed to send Discord notification: {e}")

    def get_document_summary(self, file_path, user_id, language):
        try:
            logger.info(f"Getting document summary for file: {file_path}")
            # Upload a file using lamma_index_service
            file_upload_response = lamma_index_service.upload_file(
                file_path
            )
            logger.debug(f"File Upload Response: {file_upload_response}")

            # Extract job ID from the response
            job_id = file_upload_response.get("id")
            if not job_id:
                logger.error("Failed to retrieve job ID from upload response")
                return {
                    "status": False,
                    "message": "Failed to retrieve job ID from the upload response.",
                    "data": None,
                }

            # Check job status at 5-second intervals until success
            while True:
                job_status_response = lamma_index_service.get_job_status(job_id)
                logger.debug(f"Job Status Response: {job_status_response}")

                if job_status_response.get("status") == "SUCCESS":
                    break
                elif job_status_response.get("status") == "FAILED":
                    logger.error("Job processing failed")
                    return {
                        "status": False,
                        "message": "Job failed during processing.",
                        "data": None,
                    }

                time.sleep(5)

            # Get markdown result of a job
            markdown_result_response = lamma_index_service.get_job_result_markdown(
                job_id
            )
            logger.info(f"Markdown Result Response: {markdown_result_response}")

            document_response = markdown_result_response.get("markdown", None)

            # prompt = langfuse_manager.get_prompt("lab_test_data_extraction", version=1)
            prompt = lab_extraction
            callback_handler = langfuse_manager.callback_handler(
                additional_params={
                    "session_id": "document data extraction",
                    "user_id": user_id,
                    "tags": ["OCR", "Document data querying"],
                }
            )

            # Test OpenAI
            try:
                openai_llm = ChatOpenAI(max_retries=0)
                openai_llm.invoke("Hi!")
            except (
                APIConnectionError,
                APITimeoutError,
                AuthenticationError,
                BadRequestError,
                ConflictError,
                InternalServerError,
                NotFoundError,
                PermissionDeniedError,
                RateLimitError,
                UnprocessableEntityError,
            ) as e:
                error_msg = f"🔴 OpenAI API Error: {str(e)}"
                logger.info(error_msg)
                self.send_error_notification(error_msg)

            # Test Claude
            try:
                claude_llm = ChatAnthropic(
                    model="claude-3-7-sonnet-latest",
                    api_key=self.claude_api_key,
                    max_retries=0
                )
                claude_llm.invoke("Hi!")
            except (
                APIError,
                AnthropicConnectionError,
                APIStatusError,
                AnthropicAuthError,
                AnthropicBadRequestError,
                AnthropicPermissionError,
                AnthropicNotFoundError,
                AnthropicUnprocessableError,
                AnthropicRateLimitError,
                AnthropicInternalError,
                AnthropicTimeoutError
            ) as e:
                error_msg = f"🟣 Anthropic Claude Error: {str(e)}"
                logger.info(error_msg)
                self.send_error_notification(error_msg)

            except Exception as e:
                # Handle other unexpected exceptions
                error_msg = f"⚫ Unexpected error: {str(e)}"
                logger.info(error_msg)
                self.send_error_notification(error_msg)

            # Primary and fallback model setup
            primary_model = self._get_model(
                type=ModelType.CLAUDE,
                model_name="claude-3-7-sonnet-latest",
                max_tokens=16384,
            )
            fallback_model = self._get_model(
                type=ModelType.GROQ,
                model_name="llama-3.3-70b-versatile",
                max_tokens=32768,
            )
            # self._get_model(
            #     type=ModelType.OPENAI,
            #     model_name="gpt-4o-2024-05-13",
            # )

            # Setup the primary and fallback models
            model_with_fallback = primary_model.with_fallbacks([fallback_model])

            # Setup the JSON output parser
            parser = JsonOutputParser(pydantic_object=UploadedFileContent)

            # Define a clear prompt to get JSON output
            prompt = PromptTemplate(
                template=prompt,
                # metadata={"langfuse_prompt": prompt},  # Log the prompt generation
                partial_variables={
                    "format_instructions": parser.get_format_instructions()
                },
            )

            # Build the chain with prompt, model, and parser
            chain = prompt | model_with_fallback | parser

            # Call the chain with input data and handle parsing errors
            try:
                logger.info("Invoking LLM chain for document summary")
                result = chain.invoke(
                    {
                        "lab_test_report_data": document_response,
                        "language": language,
                    },
                    config={"callbacks": [callback_handler]},
                )
            except Exception as e:
                logger.warning(
                    f"Initial parsing failed, attempting to fix output: {str(e)}"
                )
                # Create fixing parser and attempt to fix the output
                fixing_parser = OutputFixingParser.from_llm(
                    parser=parser, llm=ChatOpenAI()
                )
                result = fixing_parser.parse(e.args[0])
                logger.info("Successfully fixed and parsed output")

            # Convert test_date to string if it exists and is datetime
            if result.get("test_date") and isinstance(
                result.get("test_date"), datetime.datetime
            ):
                result["test_date"] = result["test_date"].isoformat()
                logger.debug("Converted test_date to ISO format")

            # if len(document_response) > 3000:
            #     # For long documents, process the remaining portion through batch processing
            #     batch_result = self.get_document_batch_reports(
            #         document_response, user_id, language
            #     )
            #     if (
            #         batch_result.get("status")
            #         and batch_result.get("data")
            #         and hasattr(batch_result["data"], "lab_reports")
            #     ):
            #         # Create a set of tuples with name and test_type for existing results
            #         existing_tests = {
            #             (test.name, test.test_type) for test in result.lab_reports
            #         }

            #         # Only add batch results that don't already exist
            #         for batch_test in batch_result["data"].lab_reports:
            #             if (
            #                 batch_test.name,
            #                 batch_test.test_type,
            #             ) not in existing_tests:
            #                 result.lab_reports.append(batch_test)

            logger.info("Document summary successfully generated")
            return {
                "status": True,
                "message": "Document summary successfully retrieved.",
                "data": result,
            }

        except Exception as e:
            logger.exception(e)
            return {"status": False, "message": str(e), "data": None}

    def get_document_batch_reports(self, markdown, user_id, language):
        try:
            # prompt = langfuse_manager.get_prompt("lab_test_data_extraction", version=1)
            prompt = lab_extraction
            callback_handler = langfuse_manager.callback_handler(
                additional_params={
                    "session_id": "document data extraction",
                    "user_id": user_id,
                    "tags": ["OCR", "Document data querying"],
                }
            )

            # Primary and fallback model setup
            primary_model = self._get_model(
                type=ModelType.OPENAI, model_name="gpt-4o-2024-05-13"
            )
            fallback_model = self._get_model(
                type=ModelType.CLAUDE, model_name="claude-3-5-haiku-latest"
            )
            model_with_fallback = primary_model.with_fallbacks([fallback_model])

            # Setup the JSON output parser
            parser = JsonOutputParser(pydantic_object=LabTestReportExtraction)

            # Define a clear prompt to get JSON output
            prompt = PromptTemplate(
                template=prompt,
                # metadata={"langfuse_prompt": prompt},  # Log the prompt generation
                partial_variables={
                    "format_instructions": parser.get_format_instructions()
                },
            )

            # Build the chain with prompt, model, and parser
            chain = prompt | model_with_fallback | parser

            lines, count = split_text_with_count(markdown)
            split_point = int(count * 0.6)  # Get last 40% by starting at 60% point
            response_data = "#".join(lines[split_point:])

            # Process data in chunks of 3000 characters
            chunk_size = 3000
            all_results = []

            # Split response_data into chunks
            for i in range(0, len(response_data), chunk_size):
                chunk = response_data[i : i + chunk_size]

                try:
                    logger.info(
                        f"Invoking LLM chain for document chunk {i//chunk_size + 1}"
                    )
                    chunk_result = chain.invoke(
                        {
                            "lab_test_report_data": chunk,
                            "language": language,
                        },
                        config={"callbacks": [callback_handler]},
                    )
                    all_results.append(chunk_result)
                except Exception as e:
                    logger.warning(
                        f"Initial parsing failed for chunk {i//chunk_size + 1}, attempting to fix output: {str(e)}"
                    )
                    # Create fixing parser and attempt to fix the output
                    fixing_parser = OutputFixingParser.from_llm(
                        parser=parser, llm=ChatOpenAI()
                    )
                    chunk_result = fixing_parser.parse(e.args[0])
                    all_results.append(chunk_result)
                    logger.info(
                        f"Successfully fixed and parsed output for chunk {i//chunk_size + 1}"
                    )

            # Combine results from all chunks
            result = all_results[0]  # Start with first result
            for next_result in all_results[1:]:
                # Merge the dictionaries, combining lists and updating values
                for key, value in next_result.items():
                    if key in result:
                        if isinstance(result[key], list):
                            result[key].extend(value)
                        else:
                            result[key] = value
                    else:
                        result[key] = value

            logger.info("Document summary successfully generated")
            return {
                "status": True,
                "message": "Document summary successfully retrieved.",
                "data": result,
            }

        except Exception as e:
            logger.exception(e)
            return {"status": False, "message": str(e), "data": None}

    def match_physician_info(self, physician_info, user_id, language):
        try:
            # Check if physician info is empty or None
            if physician_info is None:
                return {
                    "status": False,
                    "message": "Physician info is None",
                    "data": {
                        "matched_id": None,
                        "matched_title": None,
                        "matched_name": None,
                        "matched_lastname": None,
                        "match_info": {
                            "match_score": "Unknown",
                            "reason": "Physician info is None",
                        },
                    },
                }

            if not physician_info.get("first_name") and not physician_info.get(
                "last_name"
            ):
                return {
                    "status": True,
                    "message": "No physician data found in document",
                    "data": {
                        "matched_id": None,
                        "matched_title": None,
                        "matched_name": None,
                        "matched_lastname": None,
                        "match_info": {
                            "match_score": "Unknown",
                            "reason": "Physician data is not found in uploaded document",
                        },
                    },
                }

            prompt = langfuse_manager.get_prompt("physician_matching", version=3)
            callback_handler = langfuse_manager.callback_handler(
                additional_params={
                    "session_id": "physician matching",
                    "user_id": user_id,
                    "tags": ["OCR", "matching physician info"],
                }
            )

            # Primary and fallback model setup
            primary_model = self._get_model(
                type=ModelType.OPENAI, model_name="gpt-4o-2024-05-13"
            )
            fallback_model = self._get_model(
                type=ModelType.CLAUDE, model_name="claude-3-5-haiku-latest"
            )
            model_with_fallback = primary_model.with_fallbacks([fallback_model])

            # Setup the JSON output parser
            parser = JsonOutputParser(pydantic_object=MatchingPhysicianInfo)

            # Define a clear prompt to get JSON output
            prompt = PromptTemplate(
                template=prompt.get_langchain_prompt(),
                metadata={"langfuse_prompt": prompt},  # Log the prompt generation
                partial_variables={
                    "format_instructions": parser.get_format_instructions()
                },
            )

            # Get vectorized doctor data using VectorDataManager
            doctors_vectorstore = VectorDataManager.get_vectorized_data(
                VectorDataType.DOCTORS
            )

            # Create a retriever chain that processes the physician list
            retriever_chain = RunnablePassthrough() | doctors_vectorstore.as_retriever()

            # Build the chain with prompt, model, and parser using proper Runnable syntax
            chain = (
                {
                    "context": retriever_chain,
                    "physician_info": lambda x: physician_info,
                    "language": lambda x: language,
                    "question": RunnablePassthrough(),
                }
                | prompt
                | model_with_fallback
                | parser
            )

            # Call the chain with input data
            try:
                result = chain.invoke(
                    f"Which physician can be mapped with {physician_info['first_name']} {physician_info['last_name']}?",
                    config={"callbacks": [callback_handler]},
                )
            except Exception as e:
                logger.exception(e)
                print({"JSON": e.args[0]})
                # Return a JSON response with reason and empty tasks array
                new_parser = OutputFixingParser.from_llm(
                    parser=parser, llm=ChatOpenAI()
                )

                result = new_parser.parse(e.args[0])

            return {
                "status": True,
                "message": "Physician info matched successfully",
                "data": result,
            }

        except Exception as e:
            logger.exception(e)
            return {"status": False, "message": str(e), "data": None}

    def match_medical_facility_info(self, institution_info, user_id, language):
        try:
            if not institution_info:
                return {
                    "status": True,
                    "message": "No facility data found in document",
                    "data": {
                        "value_name": None,
                        "matched_display_name": None,
                        "matched_id": None,
                        "match_info": {
                            "match_score": "Unknown",
                            "reason": "Facility data is not found in uploaded document",
                        },
                    },
                }

            if not institution_info.get("facility_name"):
                return {
                    "status": True,
                    "message": "No facility data found in document",
                    "data": {
                        "value_name": None,
                        "matched_display_name": None,
                        "matched_id": None,
                        "match_info": {
                            "match_score": "Unknown",
                            "reason": "Facility data is not found in uploaded document",
                        },
                    },
                }

            prompt = langfuse_manager.get_prompt("medical_facility_matching", version=2)
            callback_handler = langfuse_manager.callback_handler(
                additional_params={
                    "session_id": "medical facility matching",
                    "user_id": user_id,
                    "tags": ["OCR", "matching medical institution info"],
                }
            )

            # Primary and fallback model setup
            primary_model = self._get_model(
                type=ModelType.OPENAI, model_name="gpt-4o-2024-05-13"
            )
            fallback_model = self._get_model(
                type=ModelType.CLAUDE, model_name="claude-3-5-haiku-latest"
            )
            model_with_fallback = primary_model.with_fallbacks([fallback_model])

            # Setup the JSON output parser
            parser = JsonOutputParser(pydantic_object=MatchMedicalFacility)

            # Define a clear prompt to get JSON output
            prompt = PromptTemplate(
                template=prompt.get_langchain_prompt(),
                metadata={"langfuse_prompt": prompt},  # Log the prompt generation
                partial_variables={
                    "format_instructions": parser.get_format_instructions()
                },
            )

            # Get vectorized doctor data using VectorDataManager
            institution_vectorstore = VectorDataManager.get_vectorized_data(
                VectorDataType.INSTITUTIONS
            )

            # Create a retriever chain that processes the physician list
            retriever_chain = (
                RunnablePassthrough() | institution_vectorstore.as_retriever()
            )

            # Build the chain with prompt, model, and parser using proper Runnable syntax
            chain = (
                {
                    "context": retriever_chain,
                    "institution_info": lambda x: institution_info,
                    "language": lambda x: language,
                    "question": RunnablePassthrough(),
                }
                | prompt
                | model_with_fallback
                | parser
            )

            # Call the chain with input data
            try:
                result = chain.invoke(
                    f"Which institution can be mapped with {institution_info['facility_name']}?",
                    config={"callbacks": [callback_handler]},
                )
            except Exception as e:
                logger.exception(e)
                print({"JSON": e.args[0]})
                # Return a JSON response with reason and empty tasks array
                new_parser = OutputFixingParser.from_llm(
                    parser=parser, llm=ChatOpenAI()
                )

                result = new_parser.parse(e.args[0])

            return {
                "status": True,
                "message": "Institution info matched successfully",
                "data": result,
            }

        except Exception as e:
            logger.exception(e)
            return {"status": False, "message": str(e), "data": None}

    def match_lab_reports_info(self, lab_report_info, user_id, language):
        try:
            # Lab report matching prompt setup
            prompt = lab_report_matching
            callback_handler = langfuse_manager.callback_handler(
                additional_params={
                    "session_id": "lab reports matching",
                    "user_id": user_id,
                    "tags": ["OCR", "lab reports institution info", lab_report_info.get("name", "Unknown Lab Report")],
                }
            )
            logger.info("Proceeding to vectorization of lab report data")

            # Primary and fallback model setup
            primary_model = self._get_model(
                type=ModelType.OPENAI,
                model_name="gpt-4o-mini",
                max_tokens=16384,
            )
            fallback_model = self._get_model(
                type=ModelType.GROQ,
                model_name="llama-3.3-70b-versatile",
                max_tokens=32768,
            )
            model_with_fallback = primary_model.with_fallbacks([fallback_model])

            # Setup the JSON output parser for lab report content
            parser = JsonOutputParser(pydantic_object=LabReportItem)

            logger.info("Setting up the LLM chain for lab report matching")

            # Define a clear prompt to get JSON output
            try:
                logger.info("Setting up prompt template")
                prompt = PromptTemplate(
                    template=prompt,
                    partial_variables={
                        "format_instructions": parser.get_format_instructions()
                    },
                )
                logger.info("Prompt setup complete")
            except Exception as e:
                logger.error(f"Failed to set up prompt: {e}")

            # Get vectorized doctor data using VectorDataManager
            lab_test_vectorstore = VectorDataManager.get_vectorized_data(
                VectorDataType.LAB_REPORTS
            )

            # Create a retriever chain that processes the physician list
            retriever_chain = (
                RunnablePassthrough() | lab_test_vectorstore.as_retriever(search_kwargs={'k': 10})
            )

            # Build the chain with prompt, model, and parser using proper Runnable syntax
            chain = (
                {
                    "context": retriever_chain,
                    "lab_report_data": lambda x: lab_report_info,
                    "language": lambda x: language,
                    "question": RunnablePassthrough(),
                }
                | prompt
                | model_with_fallback
                | parser
            )

            # Call the chain with input data and implement retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    result = chain.invoke(
                        f"Which lab report is related to the lab test parameter {lab_report_info.get('name', None)} using the context data?",
                        config={"callbacks": [callback_handler]},
                    )
                    logger.info(f"The length of the lab report result is: {len(result)}")
                    logger.info(f"The data type of the lab report result is: {type(result)}")
                    logger.info(f"The lab report result is: {result}")

                    # Validate the result format using validate_data_schema
                    if data_processor.validate_data_schema(result, LabReportItem):
                        break  # Exit loop if successful and valid
                    else:
                        raise ValueError("Result format validation failed.")

                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed: {e}")
                    if attempt == max_retries - 1:
                        logger.error("Max retries reached. Attempting to fix output with OutputFixingParser.")
                        new_parser = OutputFixingParser.from_llm(
                            parser=parser, llm=ChatOpenAI()
                        )
                        try:
                            result = new_parser.parse(e.args[0])
                            logger.info("Successfully fixed and parsed output.")

                            # Validate the fixed result format
                            if data_processor.validate_data_schema(result, LabReportItem):
                                break
                            else:
                                raise ValueError("Fixed result format validation failed.")

                        except Exception as fix_exception:
                            logger.error(f"Failed to fix output: {fix_exception}")
                            return {"status": False, "message": str(fix_exception), "data": None}

            return {
                "status": True,
                "message": "Institution info matched successfully",
                "data": result,
            }

        except Exception as e:
            return {"status": False, "message": str(e), "data": None}

    def process_ocr(self, test_id, user_id, file_content, language):
        # Initialize Redis session
        redis_manager = RedisSessionManager(session_id=str(test_id))

        try:

            # Update physician matching stage status
            physician_stage = StageDataPhysician(
                progress=0, status=StageStatus.IN_PROGRESS, data=None
            )
            redis_manager.update_session_data("matched_physician", physician_stage)
            redis_manager.update_session_data(
                "analysis",
                StageReport(
                    stage=ProcessingStage.PHYSICIAN_MATCHING,
                    message="Started matching physician information",
                ),
            )

            # Notify physician matching started
            webhook_service.notify_stage_update(
                stage="physician_matching",
                status=StageStatus.IN_PROGRESS.value,
                progress=0,
                test_id=test_id,
                user_id=str(user_id),
            )

            matched_physician = self.match_physician_info(
                user_id=user_id,
                language=language,
                physician_info=file_content["data"]["physician_info"],
            )

            if not matched_physician.get("status", False):
                print(
                    "Error in matching physician:",
                    matched_physician.get("message", "Unknown error"),
                )
                error_message = matched_physician.get("message", "Unknown error")
                physician_stage.progress = 100
                physician_stage.status = StageStatus.FAILED
                redis_manager.update_session_data("matched_physician", physician_stage)
                redis_manager.update_session_data(
                    "analysis",
                    StageReport(
                        stage=ProcessingStage.PHYSICIAN_MATCHING,
                        message=f"Failed to match physician: {error_message}",
                    ),
                )

                # Notify physician matching failed
                webhook_service.notify_stage_update(
                    stage="physician_matching",
                    status=StageStatus.FAILED.value,
                    progress=100,
                    test_id=test_id,
                    user_id=str(user_id),
                    data={"error": error_message},
                )

            # Ensure we have valid data before updating
            physician_data = matched_physician.get("data", {})
            valid_physician_data = physician_data
            # valid_physician_data = {
            #     "matched_id": physician_data.get("matched_id") if physician_data.get("matched_id") is not None else 0,
            #     "matched_title": physician_data.get("matched_title") if physician_data.get("matched_title") is not None else "",
            #     "matched_name": physician_data.get("matched_name") if physician_data.get("matched_name") is not None else "",
            #     "matched_lastname": physician_data.get("matched_lastname") if physician_data.get("matched_lastname") is not None else "",
            #     "match_info": physician_data.get("match_info", {
            #         "match_score": "Unknown",
            #         "reason": "No match info provided"
            #     })
            # }

            physician_stage.progress = 100
            physician_stage.status = StageStatus.COMPLETE
            physician_stage.data = valid_physician_data
            redis_manager.update_session_data("matched_physician", physician_stage)
            redis_manager.update_session_data(
                "analysis",
                StageReport(
                    stage=ProcessingStage.PHYSICIAN_MATCHING,
                    message="Successfully matched physician information",
                ),
            )

            # Get complete session data and validate it
            session_data = redis_manager.get_session()
            if session_data:
                # Notify physician matching completed with full session data
                webhook_service.notify_stage_update(
                    stage="physician_matching",
                    status=StageStatus.COMPLETE.value,
                    progress=100,
                    test_id=test_id,
                    user_id=str(user_id),
                    data=session_data.dict(),
                )
            print("Step 2 (Matched physician): ", matched_physician)

            # Update institution matching stage status
            institution_stage = StageDataInstitution(
                progress=0, status=StageStatus.IN_PROGRESS, data=None
            )
            redis_manager.update_session_data("matched_institution", institution_stage)
            redis_manager.update_session_data(
                "analysis",
                StageReport(
                    stage=ProcessingStage.INSTITUTION_MATCHING,
                    message="Started matching institution information",
                ),
            )

            # Notify institution matching started
            webhook_service.notify_stage_update(
                stage="institution_matching",
                status=StageStatus.IN_PROGRESS.value,
                progress=0,
                test_id=test_id,
                user_id=str(user_id),
            )

            matched_institution = self.match_medical_facility_info(
                user_id=user_id,
                language=language,
                institution_info=file_content["data"]["medical_facility"],
            )

            if not matched_institution.get("status", False):
                print(
                    "Error in matching institutions:",
                    matched_institution.get("message", "Unknown error"),
                )
                error_message = matched_institution.get("message", "Unknown error")
                institution_stage.progress = 100
                institution_stage.status = StageStatus.FAILED
                redis_manager.update_session_data(
                    "matched_institution", institution_stage
                )
                redis_manager.update_session_data(
                    "analysis",
                    StageReport(
                        stage=ProcessingStage.INSTITUTION_MATCHING,
                        message=f"Failed to match institution: {error_message}",
                    ),
                )

                # Notify institution matching failed
                webhook_service.notify_stage_update(
                    stage="institution_matching",
                    status=StageStatus.FAILED.value,
                    progress=100,
                    test_id=test_id,
                    user_id=str(user_id),
                    data={"error": error_message},
                )

            # Ensure we have valid data before updating
            institution_data = matched_institution.get("data", {})
            valid_institution_data = institution_data
            # valid_institution_data = {
            #     "matched_id": institution_data.get("matched_id") if institution_data.get("matched_id") is not None else 0,
            #     "matched_display_name": institution_data.get("matched_display_name") if institution_data.get("matched_display_name") is not None else "",
            #     "value_name": institution_data.get("value_name") if institution_data.get("value_name") is not None else "",
            #     "match_info": institution_data.get("match_info", {
            #         "match_score": "Unknown",
            #         "reason": "No match info provided"
            #     })
            # }

            institution_stage.progress = 100
            institution_stage.status = StageStatus.COMPLETE
            institution_stage.data = valid_institution_data
            redis_manager.update_session_data("matched_institution", institution_stage)
            redis_manager.update_session_data(
                "analysis",
                StageReport(
                    stage=ProcessingStage.INSTITUTION_MATCHING,
                    message="Successfully matched institution information",
                ),
            )

            # Get complete session data and validate it
            session_data = redis_manager.get_session()
            if session_data:
                # Notify institution matching completed with full session data
                webhook_service.notify_stage_update(
                    stage="institution_matching",
                    status=StageStatus.COMPLETE.value,
                    progress=100,
                    test_id=test_id,
                    user_id=str(user_id),
                    data=session_data.dict(),
                )
            print("Step 3 (Matched institution): ", matched_institution)

            # Update lab reports matching stage status
            lab_reports_stage = StageDataLabReport(
                progress=0, status=StageStatus.IN_PROGRESS, data=[]
            )
            redis_manager.update_session_data("lab_reports", lab_reports_stage)
            redis_manager.update_session_data(
                "analysis",
                StageReport(
                    stage=ProcessingStage.LAB_REPORT_MATCHING,
                    message="Started matching lab reports",
                ),
            )

            # Notify lab reports matching started
            webhook_service.notify_stage_update(
                stage="lab_reports_matching",
                status=StageStatus.IN_PROGRESS.value,
                progress=0,
                test_id=test_id,
                user_id=str(user_id),
            )

            print("Step 4.1 (Matched lab reports): Initiated")
            matched_lab_report_results = []

            if len(file_content["data"]["lab_reports"]) > 0:
                total_reports = len(file_content["data"]["lab_reports"])

                def process_lab_report(report, index):
                    try:
                        progress = int((index / total_reports) * 100)
                        logger.info(
                            f"Processing lab report {index} of {total_reports} ({progress}% complete)"
                        )
                        result = self.match_lab_reports_info(
                            user_id=user_id,
                            language=language,
                            lab_report_info=report,
                        )

                        if result.get("status", True):
                            data = result.get("data", {})
                            data["test_params"] = report
                            logger.debug(f"Lab report data for {index}: {data}")

                            matched_lab_report_results.append(data)

                        return result
                    except Exception as e:
                        logger.error(f"Error processing lab report {index}: {e}")
                        return None

                # Create and start thread tasks for parallel processing
                for i, lab_report in enumerate(file_content["data"]["lab_reports"], 1):
                    task_manager.add_thread_task(process_lab_report, lab_report, i)
                    logger.info(f"Added lab report {i} to thread processing queue")

                # Wait for all threads to complete
                logger.info(
                    f"Waiting for {total_reports} lab reports to be processed..."
                )
                task_manager.join_all_threads()

                logger.info(
                    f"Completed processing {len(matched_lab_report_results)} lab reports"
                )

                # Verify the structure of matched_lab_report_results
                for i, report in enumerate(matched_lab_report_results):
                    if not isinstance(report, dict):
                        logger.error(
                            f"Invalid report structure at index {i}: {report} (type: {type(report)})"
                        )

                # Update redis stage after all processing is complete
                lab_reports_stage.progress = 100
                lab_reports_stage.status = StageStatus.COMPLETE
                lab_reports_stage.data = [
                    report if isinstance(report, dict) else report.dict()
                    for report in matched_lab_report_results
                ]
                redis_manager.update_session_data("lab_reports", lab_reports_stage)
                redis_manager.update_session_data(
                    "analysis",
                    StageReport(
                        stage=ProcessingStage.LAB_REPORT_MATCHING,
                        message="Successfully matched all lab reports",
                    ),
                )

            else:
                logger.warning("No lab reports found in the document")
                lab_reports_stage.progress = 100
                lab_reports_stage.status = StageStatus.COMPLETE
                lab_reports_stage.data = []
                redis_manager.update_session_data("lab_reports", lab_reports_stage)
                redis_manager.update_session_data(
                    "analysis",
                    StageReport(
                        stage=ProcessingStage.LAB_REPORT_MATCHING,
                        message="No lab reports found to process",
                    ),
                )

                # Notify lab reports matching failed
                webhook_service.notify_stage_update(
                    stage="lab_reports_matching",
                    status=StageStatus.FAILED.value,
                    progress=100,
                    test_id=test_id,
                    user_id=str(user_id),
                    data={"error": "No lab reports found in the document"},
                )

            logger.info(
                "Step 4.2 (Matched lab reports): %s", matched_lab_report_results
            )

            session_data = redis_manager.get_session()
            if session_data:
                # Notify process completion with full session data
                webhook_service.notify_process_complete(
                    test_id=test_id,
                    user_id=str(user_id),
                    success=True,
                    data=session_data.dict(),
                )

            return {
                "physician_info": matched_physician,
                "medical_facility": matched_institution,
                "lab_reports": matched_lab_report_results,
            }

        except Exception as e:
            logger.exception(e)
            error_message = str(e)

            redis_manager.update_session_data(
                "analysis",
                StageReport(
                    stage=ProcessingStage.LAB_REPORT_MATCHING,
                    message=f"Processing failed with error: {error_message}",
                ),
            )

            # Notify error occurred
            webhook_service.notify_error(
                test_id=test_id,
                user_id=str(user_id),
                error_message=error_message,
                stage="task",
            )


llm_service = LLMServices()
