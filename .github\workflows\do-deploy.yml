name: Mediboard OCR-LLM Server Deployment Pipeline - Digital Ocean

on:
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      ECR_REPO: ${{ secrets.MEDIBOARD_AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.MEDIBOARD_AWS_REGION }}.amazonaws.com/mediboard-ocr-llm
      AWS_REGION: ${{ secrets.MEDIBOARD_AWS_REGION }}
      REMOTE_DIR: /opt/ocr

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.MEDIBOARD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.MEDIBOARD_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.MEDIBOARD_AWS_REGION }}

      - name: Login to ECR
        uses: aws-actions/amazon-ecr-login@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Docker Image to ECR
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ env.ECR_REPO }}:latest

      - name: Fetch secrets and create .env
        run: |
          SECRET_JSON=$(aws secretsmanager get-secret-value \
            --secret-id ${{ secrets.MEDIBOARD_OCR_SECRETS }} \
            --query SecretString --output text)
          echo "$SECRET_JSON" | jq -r '. | to_entries | .[] | "\(.key)=\(.value)"' > .env

      - name: Transfer project files
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DO_HOST }}
          username: ${{ secrets.DO_USERNAME }}
          key: ${{ secrets.DO_SSH }}
          source: "monitoring-stack,nginx.conf,docker-compose.yml,deploy.sh,.env"
          target: "${{ env.REMOTE_DIR }}"
          strip_components: 0
          overwrite: true

      - name: Deploy to Droplet via SSH
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.DO_HOST }}
          username: ubuntu
          key: ${{ secrets.DO_SSH }}
          script: |
            cd ${REMOTE_DIR}
            chmod +x deploy.sh
            ./deploy.sh

    # - name: Notify Slack
    #   uses: rtCamp/action-slack-notify@v2
    #   env:
    #     SLACK_CHANNEL: mediboard-notifications
    #     SLACK_COLOR: ${{ job.status }}
    #     SLACK_TITLE: MEDIBOARD-OCR-LLM Deployment Status
    #     SLACK_ICON_EMOJI: ':rocket:'
    #     SLACK_MESSAGE_ON_SUCCESS: 'MEDIBOARD-OCR-LLM has been deployed successfully to DigitalOcean!'
    #     SLACK_WEBHOOK: ${{ secrets.BUILD_NOTIFS_SLACK_WEBHOOK }}
