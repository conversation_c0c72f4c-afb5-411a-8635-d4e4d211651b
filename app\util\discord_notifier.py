import requests
from app.core.config import settings
from app.util.logger import logger

def send_discord_notification(message: str) -> None:
    """
    Send a notification message to Discord using webhook.
    
    Args:
        message (str): The message to send to Discord
    """
    data = {"content": f"{message}"}
    try:
        response = requests.post(settings.DISCORD_WEBHOOK_URL, json=data)
        if response.status_code == 204:
            logger.info("Message sent successfully to Discord!")
        else:
            logger.error(f"Failed to send message to Discord. Status code: {response.status_code}")
    except Exception as e:
        logger.error(f"Error sending message to Discord: {e}")
