import os
import asyncio
import json
import time
from pathlib import Path
from typing import List, Dict, Any
from pyzerox import zerox

class ZeroxBenchmark:
    def __init__(self, model: str = None, api_key: str = None):
        """Initialize the benchmark with configurable model and API key"""
        
        # Model configuration - choose your provider and model
        if model:
            self.model = model
        else:
            # Default models (uncomment the one you want to use)
            self.model = "claude-sonnet-4-20250514"  # Anthropic Claude
            # self.model = "gpt-4o-mini"  # OpenAI GPT-4o mini
            # self.model = "gpt-4o"  # OpenAI GPT-4o
        
        # API Key setup - set the appropriate key based on your model
        if api_key:
            self._set_api_key(api_key)
    def _set_api_key(self, api_key: str):
        """Set API key based on the model provider"""
        if "claude" in self.model.lower() or "anthropic" in self.model.lower():
            os.environ["ANTHROPIC_API_KEY"] = api_key
        elif "gpt" in self.model.lower():
            os.environ["OPENAI_API_KEY"] = api_key
    
    def _setup_provider_config(self):
        """Setup provider-specific configuration"""
        
        # Batch settings
        self.batch_size = 5
        self.output_base_dir = "./zerox_benchmark_output"
        
        # Create output directory
        Path(self.output_base_dir).mkdir(exist_ok=True)
        
        # Benchmark results
        self.results = []
    
    def find_files(self, directory: str, extensions: List[str] = None) -> List[str]:
        """Find all files with specified extensions in a directory"""
        if extensions is None:
            extensions = ['.pdf', '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']
        
        files = []
        path = Path(directory)
        
        if not path.exists():
            print(f"Directory {directory} does not exist")
            return files
            
        for ext in extensions:
            files.extend(path.glob(f"**/*{ext}"))
            files.extend(path.glob(f"**/*{ext.upper()}"))
        
        return [str(f) for f in files]
    
    async def process_single_file(self, file_path: str, output_dir: str) -> Dict[str, Any]:
        """Process a single file and return benchmark data"""
        start_time = time.time()
        
        try:
            print(f"Processing: {file_path}")
            
            # Create output directory for this file
            file_output_dir = Path(output_dir) / Path(file_path).stem
            file_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Process the file
            result = await zerox(
                file_path=file_path,
                model=self.model,
                output_dir=str(file_output_dir),
                cleanup=True,
                concurrency=3  # Lower concurrency for stability
            )
            
            processing_time = time.time() - start_time
            
            # Calculate file size
            file_size = Path(file_path).stat().st_size
            
            benchmark_data = {
                'file_path': file_path,
                'file_name': Path(file_path).name,
                'file_size_mb': round(file_size / (1024 * 1024), 2),
                'processing_time': round(processing_time, 2),
                'completion_time': result.completion_time if hasattr(result, 'completion_time') else None,
                'input_tokens': result.input_tokens if hasattr(result, 'input_tokens') else None,
                'output_tokens': result.output_tokens if hasattr(result, 'output_tokens') else None,
                'pages_processed': len(result.pages) if hasattr(result, 'pages') else None,
                'output_dir': str(file_output_dir),
                'status': 'success'
            }
            
            print(f"✓ Successfully processed {file_path} in {processing_time:.2f}s")
            return benchmark_data
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_data = {
                'file_path': file_path,
                'file_name': Path(file_path).name,
                'processing_time': round(processing_time, 2),
                'error': str(e),
                'status': 'error'
            }
            print(f"✗ Error processing {file_path}: {str(e)}")
            return error_data
    
    async def process_batch(self, batch: List[str], batch_num: int) -> List[Dict[str, Any]]:
        """Process a batch of files concurrently"""
        print(f"\n--- Processing Batch {batch_num} ({len(batch)} files) ---")
        
        batch_output_dir = Path(self.output_base_dir) / f"batch_{batch_num}"
        batch_output_dir.mkdir(exist_ok=True)
        
        tasks = []
        for file_path in batch:
            task = self.process_single_file(file_path, str(batch_output_dir))
            tasks.append(task)
        
        # Process files in the batch concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        batch_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                batch_results.append({
                    'file_path': batch[i],
                    'file_name': Path(batch[i]).name,
                    'error': str(result),
                    'status': 'exception'
                })
            else:
                batch_results.append(result)
        
        return batch_results
    
    def create_batches(self, files: List[str]) -> List[List[str]]:
        """Split files into batches of specified size"""
        batches = []
        for i in range(0, len(files), self.batch_size):
            batch = files[i:i + self.batch_size]
            batches.append(batch)
        return batches
    
    def save_benchmark_results(self):
        """Save benchmark results to JSON file"""
        results_file = Path(self.output_base_dir) / "benchmark_results.json"
        
        summary = {
            'total_files': len(self.results),
            'successful_files': len([r for r in self.results if r.get('status') == 'success']),
            'failed_files': len([r for r in self.results if r.get('status') in ['error', 'exception']]),
            'total_processing_time': sum(r.get('processing_time', 0) for r in self.results),
            'results': self.results
        }
        
        with open(results_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📊 Benchmark results saved to: {results_file}")
        return summary
    
    def print_summary(self):
        """Print benchmark summary"""
        successful = [r for r in self.results if r.get('status') == 'success']
        failed = [r for r in self.results if r.get('status') in ['error', 'exception']]
        
        print("\n" + "="*60)
        print("BENCHMARK SUMMARY")
        print("="*60)
        print(f"Total files processed: {len(self.results)}")
        print(f"Successful: {len(successful)}")
        print(f"Failed: {len(failed)}")
        
        if successful:
            avg_time = sum(r.get('processing_time', 0) for r in successful) / len(successful)
            total_tokens = sum(r.get('input_tokens', 0) + r.get('output_tokens', 0) for r in successful if r.get('input_tokens') and r.get('output_tokens'))
            print(f"Average processing time: {avg_time:.2f}s")
            print(f"Total tokens used: {total_tokens}")
        
        if failed:
            print("\nFailed files:")
            for f in failed:
                print(f"  - {f['file_name']}: {f.get('error', 'Unknown error')}")
    
    async def run_benchmark(self, input_directory: str):
        """Run the complete benchmark"""
        print(f"🚀 Starting Zerox benchmark with model: {self.model}")
        print(f"📁 Input directory: {input_directory}")
        print(f"📄 Batch size: {self.batch_size}")
        print(f"💾 Output directory: {self.output_base_dir}")
        
        # Find all files
        files = self.find_files(input_directory)
        if not files:
            print("❌ No files found in the specified directory")
            return
        
        print(f"📋 Found {len(files)} files to process")
        
        # Create batches
        batches = self.create_batches(files)
        print(f"📦 Created {len(batches)} batches")
        
        # Process batches
        start_time = time.time()
        for i, batch in enumerate(batches, 1):
            batch_results = await self.process_batch(batch, i)
            self.results.extend(batch_results)
            
            # Add delay between batches to avoid rate limiting
            if i < len(batches):
                print(f"⏳ Waiting 2 seconds before next batch...")
                await asyncio.sleep(2)
        
        total_time = time.time() - start_time
        print(f"\n⏰ Total benchmark time: {total_time:.2f}s")
        
        # Save and print results
        self.save_benchmark_results()
        self.print_summary()


async def main():
    """Main function to run the benchmark"""
    # Initialize benchmark with your chosen model and API key
    
    # Example configurations (uncomment the one you want to use):
    
    # Anthropic Claude
    benchmark = ZeroxBenchmark(model="claude-3-5-sonnet-20241022")
    # benchmark = ZeroxBenchmark(model="claude-3-5-sonnet-20241022", api_key="your-anthropic-key")
    
    # OpenAI GPT
    # benchmark = ZeroxBenchmark(model="gpt-4o-mini")
    # benchmark = ZeroxBenchmark(model="gpt-4o-mini", api_key="your-openai-key")
    
    # Or use default model (Claude) with environment variable
    # benchmark = ZeroxBenchmark()
    
    # Set your input directory containing PDFs and images
    input_directory = "./test_files"  # Change this to your directory
    
    # Create test directory with instructions if it doesn't exist
    if not Path(input_directory).exists():
        Path(input_directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {input_directory}")
        print(f"📄 Please add your PDF and image files to {input_directory} and run the script again.")
        return
    
    # Run the benchmark
    await benchmark.run_benchmark(input_directory)


if __name__ == "__main__":
    print("🔧 Zerox Library Benchmark Tool - OpenAI & Anthropic")
    print("=" * 50)
    
    # Display API key setup instructions
    print("🔑 API Key Setup Instructions:")
    print("=" * 30)
    print("Choose your provider and set the appropriate environment variable:")
    print("• Anthropic Claude: export ANTHROPIC_API_KEY='your-key'")
    print("• OpenAI GPT: export OPENAI_API_KEY='your-key'")
    print("• Or pass the key directly in the script")
    print()
    
    # Check for API keys
    api_keys_found = []
    if os.environ.get("ANTHROPIC_API_KEY"):
        api_keys_found.append("Anthropic")
    # if os.environ.get("OPENAI_API_KEY"):  # Commented out placeholder
    #     api_keys_found.append("OpenAI")
    
    if api_keys_found:
        print(f"✅ Found API keys for: {', '.join(api_keys_found)}")
    else:
        print("⚠️  No API keys found in environment variables")
        print("💡 Make sure to set the appropriate API key or pass it in the script")
    print()
    
    # Run the benchmark
    asyncio.run(main())