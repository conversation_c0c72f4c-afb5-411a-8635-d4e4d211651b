import requests
import json
from datetime import datetime
import time
from typing import Any, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class WebhookService:
    def __init__(self, webhook_url: str = "https://api.mediboard.ai/api/webhook/ocr"):
        self.webhook_url = webhook_url
        self.max_retries = 3
        self.retry_delay = 2  # seconds

    def _serialize_datetime(self, obj: Any) -> Any:
        """Handle datetime serialization in JSON"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    def _send_with_retry(self, payload: Dict[str, Any]) -> bool:
        """Send webhook with retry logic"""
        retries = 0
        while retries < self.max_retries:
            try:
                response = requests.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                response.raise_for_status()
                logger.info(f"Webhook sent successfully: {payload}")
                return True
            except requests.exceptions.RequestException as e:
                retries += 1
                if retries == self.max_retries:
                    logger.error(f"Failed to send webhook after {self.max_retries} retries: {str(e)}")
                    return False
                logger.warning(f"Webhook attempt {retries} failed, retrying in {self.retry_delay} seconds...")
                time.sleep(self.retry_delay * retries)  # Exponential backoff
        return False

    def notify_stage_update(
        self,
        stage: str,
        status: str,
        progress: int,
        test_id: str,
        user_id: str,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send stage-specific webhook notification"""
        payload = {
            "event": "stage_update",
            "timestamp": self._serialize_datetime(datetime.utcnow()),
            "test_id": test_id,
            "user_id": user_id,
            "stage": stage,
            "status": status,
            "progress": progress
        }
        
        if data:
            payload["data"] = data

        return self._send_with_retry(payload)

    def notify_process_complete(
        self,
        test_id: str,
        user_id: str,
        success: bool,
        message: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Send process completion webhook notification"""
        payload = {
            "event": "process_complete",
            "timestamp": self._serialize_datetime(datetime.utcnow()),
            "test_id": test_id,
            "user_id": user_id,
            "success": success
        }

        if message:
            payload["message"] = message
        if data:
            payload["data"] = data

        return self._send_with_retry(payload)

    def notify_error(
        self,
        test_id: str,
        user_id: str,
        error_message: str,
        stage: Optional[str] = None
    ) -> bool:
        """Send error webhook notification"""
        payload = {
            "event": "error",
            "timestamp": self._serialize_datetime(datetime.utcnow()),
            "test_id": test_id,
            "user_id": user_id,
            "error": error_message
        }

        if stage:
            payload["stage"] = stage

        return self._send_with_retry(payload)
