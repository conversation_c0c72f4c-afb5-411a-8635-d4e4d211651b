import json
from fastapi import APIRouter, HTTPException, Form
from app.services.llm_services import LLMServices
from app.util.logger import logger

test_router = APIRouter(
    prefix="/test", responses={404: {"description": "Not found"}}, tags=["test"]
)

@test_router.post("/match-lab-reports")
async def match_lab_reports(
    user_id: int = Form(...),
    language: str = Form("english"),
    lab_report_info: str = Form(...)
):
    try:
        # Convert lab_report_info from string to dictionary
        lab_report_info_dict = json.loads(lab_report_info)

        print(lab_report_info_dict)

        llm_service = LLMServices()
        logger.info("Starting lab report matching...")

        # Call the match_lab_reports_info method
        result = llm_service.match_lab_reports_info(
            user_id=user_id,
            language=language,
            lab_report_info=lab_report_info_dict
        )

        if not result.get("status", False):
            error_msg = result.get("message", "Unknown error")
            logger.error(f"Error in matching lab reports: {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)

        logger.info("Lab report matching completed successfully")
        return {
            "status": True,
            "message": "Lab report matched successfully.",
            "data": result.get("data")
        }

    except Exception as e:
        logger.exception(e)
        raise HTTPException(status_code=500, detail=str(e))
    
