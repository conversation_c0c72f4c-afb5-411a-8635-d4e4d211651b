{"patient_info": {"first_name": "שמואל", "last_name": "מנחם"}, "physician_info": null, "medical_facility": {"facility_name": "המר<PERSON><PERSON> הרפואי תל-אביב ע\"ש סוראסקי (איכ<PERSON>לוב)", "location": null}, "is_lab_report": true, "test_date": "2024-08-02", "lab_reports": [{"name": "RBC", "result": "4.3", "range": "4.3-5.8", "units": "10e6/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 1, "result_value_type": "numeric_value"}, {"name": "Hemoglobin", "result": "12.2", "range": "13.2-17.0", "units": "g/dL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 2, "result_value_type": "numeric_value"}, {"name": "HCT", "result": "37", "range": "39-49", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 3, "result_value_type": "numeric_value"}, {"name": "MCV", "result": "86.0", "range": "80.0-96.0", "units": "fL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 4, "result_value_type": "numeric_value"}, {"name": "MCH", "result": "28.2", "range": "26.0-34.0", "units": "pg", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 5, "result_value_type": "numeric_value"}, {"name": "MCHC", "result": "32.8", "range": "31.0-37.0", "units": "g/dL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 6, "result_value_type": "numeric_value"}, {"name": "RDW", "result": "15.8", "range": "0.0-15.0", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 7, "result_value_type": "numeric_value"}, {"name": "NRBC/100 WBC", "result": "0.1", "range": null, "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 8, "result_value_type": "numeric_value"}, {"name": "WBC", "result": "11.5", "range": "4.0-11.0", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 9, "result_value_type": "numeric_value"}, {"name": "Neutrophils %", "result": "52.4", "range": "40.0-75.0", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 10, "result_value_type": "numeric_value"}, {"name": "Neutrophils No.", "result": "6.1", "range": "1.4-6.0", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 11, "result_value_type": "numeric_value"}, {"name": "Lymphocytes %", "result": "33.5", "range": "20.0-40.0", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 12, "result_value_type": "numeric_value"}, {"name": "Lymphocytes No", "result": "3.9", "range": "1.2-3.0", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 13, "result_value_type": "numeric_value"}, {"name": "Mono%", "result": "10.6", "range": "2.0-13.0", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 14, "result_value_type": "numeric_value"}, {"name": "Monocytes No.", "result": "1.2", "range": "0.0-1.3", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 15, "result_value_type": "numeric_value"}, {"name": "Eosinophils %", "result": "2.7", "range": "0.0-6.0", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 16, "result_value_type": "numeric_value"}, {"name": "Eosinophils No.", "result": "0.3", "range": "0.0-0.4", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 17, "result_value_type": "numeric_value"}, {"name": "Basophils%", "result": "0.8", "range": "0.0-1.9", "units": "%", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 18, "result_value_type": "numeric_value"}, {"name": "Basophils No.", "result": "0.1", "range": "0.0-0.2", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 19, "result_value_type": "numeric_value"}, {"name": "Platelet, automated count", "result": "290", "range": "150-450", "units": "10e3/uL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 20, "result_value_type": "numeric_value"}, {"name": "Mean platelet volume", "result": "10.4", "range": "7.0-12.0", "units": "fL", "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 21, "result_value_type": "numeric_value"}, {"name": "Neutrophils/lymphocytes ratio - blood", "result": "1.6", "range": null, "units": null, "test_type": "ספירת דם מלאה", "comment": null, "comment_english": null, "index": 22, "result_value_type": "numeric_value"}, {"name": "Thyroid stimulating hormone (TSH) - blood", "result": "1.87", "range": "0.40-4.70", "units": "muIL", "test_type": "אנדוק<PERSON>ינולוגיה-דם", "comment": null, "comment_english": null, "index": 23, "result_value_type": "numeric_value"}, {"name": "Triiodothyronine (T3), total - blood", "result": "92.50", "range": "80.00-200.00", "units": "ng/dL", "test_type": "אנדוק<PERSON>ינולוגיה-דם", "comment": null, "comment_english": null, "index": 24, "result_value_type": "numeric_value"}, {"name": "Thyroxine (T4), free - blood", "result": "0.98", "range": "0.70-1.80", "units": "ng/dL", "test_type": "אנדוק<PERSON>ינולוגיה-דם", "comment": null, "comment_english": null, "index": 25, "result_value_type": "numeric_value"}, {"name": "Hemoglobin A1C% - B", "result": "7.2", "range": "3.9-5.7", "units": "%", "test_type": "ביו<PERSON>ימ<PERSON>ה קלינית - דם", "comment": "טווח לסוכרת: 5.8%-6.4% סוכרת: שווה/גדול מ-6.5%", "comment_english": "Diabetes range: 5.8%-6.4%. Diabetes: equal/greater than 6.5%", "index": 26, "result_value_type": "numeric_value"}, {"name": "HbA1C#", "result": "54.9", "range": "19.0-38.8", "units": "mmol/mol", "test_type": "ביו<PERSON>ימ<PERSON>ה קלינית - דם", "comment": "טווח לסוכרת: 39.9 - 46.5 סוכרת: שווה/גדול מ-47.5", "comment_english": "Diabetes range: 39.9 - 46.5. Diabetes: equal/greater than 47.5", "index": 27, "result_value_type": "numeric_value"}, {"name": "Prostate specific antigen (PSA), total - blood", "result": "0.001", "range": "0.000-4.000", "units": "ng/ml", "test_type": "סמני סרטן - דם", "comment": null, "comment_english": null, "index": 28, "result_value_type": "numeric_value"}, {"name": "Prostate specific antigen (PSA), free - blood", "result": "0.000", "range": "0.000-0.934", "units": "ng/ml", "test_type": "סמני סרטן - דם", "comment": null, "comment_english": null, "index": 29, "result_value_type": "numeric_value"}, {"name": "Prostate specific antigen (PSA), free/total ratio - blood", "result": "0.00", "range": null, "units": "%", "test_type": "סמני סרטן - דם", "comment": null, "comment_english": null, "index": 30, "result_value_type": "numeric_value"}, {"name": "CA 19.9, tumor antigen - blood", "result": "3.60", "range": "0.00-31.00", "units": "u/ml", "test_type": "סמני סרטן - דם", "comment": null, "comment_english": null, "index": 31, "result_value_type": "numeric_value"}, {"name": "Carcinoembryonic antigen (CEA) - blood", "result": "2.80", "range": "0.00-5.00", "units": "ng/ml", "test_type": "סמני סרטן - דם", "comment": null, "comment_english": null, "index": 32, "result_value_type": "numeric_value"}, {"name": "Beta CrossLaps (CTX)", "result": "834.3", "range": "118.0-776.0", "units": "pg/ml", "test_type": "אנדו<PERSON><PERSON><PERSON>נולוגיה-סמ", "comment": null, "comment_english": null, "index": 33, "result_value_type": "numeric_value"}, {"name": "Glucose - blood", "result": "88", "range": "70-110", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 34, "result_value_type": "numeric_value"}, {"name": "Urea nitrogen (BUN) - blood", "result": "21", "range": "5-25", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 35, "result_value_type": "numeric_value"}, {"name": "Sodium - blood", "result": "135.0", "range": "135.0-146.0", "units": "mmol/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 36, "result_value_type": "numeric_value"}, {"name": "Potassium - blood", "result": "5.21", "range": "3.50-5.30", "units": "mmol/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 37, "result_value_type": "numeric_value"}, {"name": "Chloride - blood", "result": "101", "range": "95-110", "units": "mmol/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 38, "result_value_type": "numeric_value"}, {"name": "Creatinine", "result": "1.13", "range": "0.70-1.30", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 39, "result_value_type": "numeric_value"}, {"name": "Uric acid - blood", "result": "4.60", "range": "2.30-8.00", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 40, "result_value_type": "numeric_value"}, {"name": "Calcium, total - blood", "result": "9.00", "range": "8.50-10.50", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 41, "result_value_type": "numeric_value"}, {"name": "Magnesium - blood", "result": "2.13", "range": "1.80-2.55", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 42, "result_value_type": "numeric_value"}, {"name": "Phosphorus - blood", "result": "4.71", "range": "2.50-4.50", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 43, "result_value_type": "numeric_value"}, {"name": "Lactate dehydrogenase (LD) - blood", "result": "333", "range": "208-378", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 44, "result_value_type": "numeric_value"}, {"name": "Creatine kinase (CK), (CPK) - blood", "result": "108", "range": "46-171", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 45, "result_value_type": "numeric_value"}, {"name": "<PERSON><PERSON><PERSON><PERSON>, total - blood", "result": "0.58", "range": "0.10-1.20", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 46, "result_value_type": "numeric_value"}, {"name": "<PERSON><PERSON><PERSON><PERSON>, direct - blood", "result": "0.25", "range": "0.10-0.30", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 47, "result_value_type": "numeric_value"}, {"name": "<PERSON><PERSON><PERSON><PERSON>, indirect - blood", "result": "0.33", "range": "0.00-0.60", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 48, "result_value_type": "numeric_value"}, {"name": "Aspartate aminotransferase, AST (GOT) - blood", "result": "27", "range": "7-40", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 49, "result_value_type": "numeric_value"}, {"name": "Alanine amino transferase, ALT (GPT) - blood", "result": "18", "range": "8-39", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 50, "result_value_type": "numeric_value"}, {"name": "Alkaline Phosphatase - blood", "result": "131.00", "range": "46.00-116.00", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 51, "result_value_type": "numeric_value"}, {"name": "Gamma glutamyltransferase (GGT) - blood", "result": "37", "range": "6-42", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 52, "result_value_type": "numeric_value"}, {"name": "<PERSON>tein, total - blood", "result": "67.2", "range": "64.0-83.0", "units": "gr/l", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 53, "result_value_type": "numeric_value"}, {"name": "Albumin", "result": "42.7", "range": "35.0-50.0", "units": "g/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 54, "result_value_type": "numeric_value"}, {"name": "Globulin - blood", "result": "24.5", "range": "20.0-31.0", "units": "gr/l", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 55, "result_value_type": "numeric_value"}, {"name": "CRP (WIDE RANGE)", "result": "0.37", "range": "0.00-5.00", "units": "mg/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 56, "result_value_type": "numeric_value"}, {"name": "Amylase - blood", "result": "51", "range": "20-104", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 57, "result_value_type": "numeric_value"}, {"name": "Lipase - blood", "result": "12", "range": "12-51", "units": "U/L", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 58, "result_value_type": "numeric_value"}, {"name": "Iron - blood", "result": "54.8", "range": "40.0-150.0", "units": "mcg/dl", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 59, "result_value_type": "numeric_value"}, {"name": "Ferritin - blood", "result": "22.30", "range": "14.00-163.00", "units": "ng/ml", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 60, "result_value_type": "numeric_value"}, {"name": "Transferrin - blood", "result": "340", "range": "220-400", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 61, "result_value_type": "numeric_value"}, {"name": "Transferrin, saturation - blood", "result": "11.5", "range": "20.0-55.0", "units": "%", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 62, "result_value_type": "numeric_value"}, {"name": "Cholesterol, total - blood", "result": "119", "range": "150-200", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 63, "result_value_type": "numeric_value"}, {"name": "Triglycerides - blood", "result": "108", "range": "50-150", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 64, "result_value_type": "numeric_value"}, {"name": "HDL cholesterol - blood", "result": "49.0", "range": "40.0-200.0", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 65, "result_value_type": "numeric_value"}, {"name": "LDL-cholesterol, calculated - blood", "result": "48", "range": "60-160", "units": "mg/dL", "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 66, "result_value_type": "numeric_value"}, {"name": "Risk factor for myocardial infarction", "result": "2.43", "range": "<4.01", "units": null, "test_type": "כימיה - דם", "comment": null, "comment_english": null, "index": 67, "result_value_type": "numeric_value"}, {"name": "eGFR", "result": "61", "range": "60-200", "units": "ml/min/1.73m^2", "test_type": "כימיה - דם", "comment": "התוצאה משמשת לבקרת המעקב הקליני בלבד ואינה מיועדת לכל שימוש אחר", "comment_english": "The result is for clinical follow-up monitoring only and is not intended for any other use", "index": 68, "result_value_type": "numeric_value"}, {"name": "Brain natriuretic peptide (BNP) - blood", "result": "60", "range": "0-80", "units": "PG/ML", "test_type": null, "comment": null, "comment_english": null, "index": 69, "result_value_type": "numeric_value"}, {"name": "Vitamin B-12, cyanocobalamin - blood", "result": "635.00", "range": "175.00-961.00", "units": "pg/ml", "test_type": null, "comment": null, "comment_english": null, "index": 70, "result_value_type": "numeric_value"}, {"name": "Folic acid - blood", "result": "19.82", "range": "2.60-17.10", "units": "ng/ml", "test_type": null, "comment": null, "comment_english": null, "index": 71, "result_value_type": "numeric_value"}, {"name": "Vitamin D-3 (Calcifediol), 25-Hydroxy - blood", "result": "41.0", "range": null, "units": "ng/ml", "test_type": "ויטמינים", "comment": "Expected Values for Adults 30-58 ng/mL; Relative insufficiency 20-29 ng/mL; Deficiency <20 ng/mL; Severe Deficiency <10 ng/mL", "comment_english": "Expected Values for Adults 30-58 ng/mL; Relative insufficiency 20-29 ng/mL; Deficiency <20 ng/mL; Severe Deficiency <10 ng/mL", "index": 72, "result_value_type": "numeric_value"}, {"name": "Thyroglobulin - blood", "result": "23.70", "range": "0.20-25.00", "units": "NG/ML", "test_type": null, "comment": "שים לב לשינוי בערכי הייחוס החל מתאריך 9/01/2017", "comment_english": "Note the change in reference values as of 9/01/2017", "index": 73, "result_value_type": "numeric_value"}]}