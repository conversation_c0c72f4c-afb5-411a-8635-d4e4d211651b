import sys
from app.core.config import settings
from langchain_anthropic import ChatAnthropic

# Hardcoded Hebrew text for testing
hebrew_text = "הבדיקה בוצעה פעמיים\nדווח ל: פרו<PERSON> לוגובסקי ב 16:15 13/09/23"

# Hebrew text for mistranslation testing, now on a single line
hebrew_text_mistranslated = "PH הבדיקה בוצעה פעמיים דוח\"ז פרטי למטופל: ב 13/09/23 16:15"

prompt = f"Translate the following Hebrew text to English: {hebrew_text_mistranslated}"

# Use Claude model and API key from settings
llm = ChatAnthropic(
    model_name="claude-3-7-sonnet-latest",  # or use your preferred Claude model
    anthropic_api_key=settings.CLAUDE_API_KEY,
)

try:
    result = llm.invoke(prompt)
    print("\nEnglish translation:\n", result)
except Exception as e:
    print("Error during translation:", e) 