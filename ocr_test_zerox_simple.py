import os
import asyncio
from pathlib import Path
from pyzerox import zerox

# --- USER SETTINGS ---
MODEL = "claude-3-5-sonnet-20241022"  # Change as needed
API_KEY = "YOUR_ANTHROPIC_API_KEY"    # Set your API key here
INPUT_FOLDER = "test_files"            # Folder with files to process
OUTPUT_FOLDER = "zerox_output"         # Where to save OCR results
SUPPORTED_EXTENSIONS = {'.pdf', '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff'}

# Set API key environment variable
os.environ["ANTHROPIC_API_KEY"] = API_KEY

def find_files(folder):
    folder = Path(folder)
    return [f for f in folder.iterdir() if f.is_file() and f.suffix.lower() in SUPPORTED_EXTENSIONS]

async def process_file(file_path: Path, output_dir: Path):
    print(f"Processing: {file_path}")
    try:
        result = await zerox(
            file_path=str(file_path),
            model=MODEL,
            output_dir=str(output_dir),
            cleanup=True,
            concurrency=3
        )
        # Save markdown or text output
        if hasattr(result, 'markdown') and result.markdown:
            out_file = output_dir / (file_path.stem + '.md')
            with open(out_file, 'w', encoding='utf-8') as f:
                f.write(result.markdown)
            print(f"Saved markdown to {out_file}")
        elif hasattr(result, 'text') and result.text:
            out_file = output_dir / (file_path.stem + '.txt')
            with open(out_file, 'w', encoding='utf-8') as f:
                f.write(result.text)
            print(f"Saved text to {out_file}")
        else:
            print(f"No markdown or text output for {file_path}")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

async def main():
    input_dir = Path(INPUT_FOLDER)
    output_dir = Path(OUTPUT_FOLDER)
    output_dir.mkdir(parents=True, exist_ok=True)
    if not input_dir.exists() or not input_dir.is_dir():
        print(f"Input folder not found: {INPUT_FOLDER}")
        return
    files = find_files(input_dir)
    if not files:
        print(f"No supported files found in {INPUT_FOLDER}")
        return
    print(f"Found {len(files)} supported files in {INPUT_FOLDER}. Starting OCR...")
    for file_path in files:
        await process_file(file_path, output_dir)

if __name__ == "__main__":
    asyncio.run(main()) 