import os
from dotenv import load_dotenv
load_dotenv()
class Settings:
    # api keys
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY")

    # langfuse
    LANGFUSE_SECRET_KEY: str = os.getenv("LANGFUSE_SECRET_KEY")
    LANGFUSE_PUBLIC_KEY: str = os.getenv("LANGFUSE_PUBLIC_KEY")
    LANGFUSE_HOST: str = "https://cloud.langfuse.com"

    # claude
    CLAUDE_API_KEY: str = os.getenv("CLAUDE_API_KEY")

    # lamma
    LLAMA_CLOUD_API_KEY: str = os.getenv("LLAMA_CLOUD_API_KEY")
    
    #nomic
    NOMIC_API_KEY: str = os.getenv("NOMIC_API_KEY")

    # Redis
    REDIS_HOST: str = os.getenv("REDIS_HOST", "redis")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB: int = int(os.getenv("REDIS_DB", 0))
    
    #slack
    SLACK_TOKEN: str = os.getenv("SLACK_TOKEN")

    # Groq
    GROQ_API_KEY: str = os.getenv("GROQ_API_KEY")
    
    # Discord
    DISCORD_WEBHOOK_URL: str = os.getenv("DISCORD_WEBHOOK_URL")

settings = Settings()
